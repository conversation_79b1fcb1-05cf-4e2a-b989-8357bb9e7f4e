<?php
session_start();
if (!isset($_SESSION['user'])) {
    $_SESSION['logerr'] = "You have been logged out. Try Again.";
    header("location:index.php");
}
?>


<!DOCTYPE html>
<html>

<head>

    <link href="datatables/ie10-viewport-bug-workaround.css" rel="stylesheet">
    <script src="datatables/ie-emulation-modes-warning.js"></script>

    <link href="datatables/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="datatables/dataTables.bootstrap4.min.css" />
    <link rel="stylesheet" href="datatables/buttons.bootstrap4.min.css" />


    <title>Add New User</title>
    <meta name='robots' content='nofollow,noindex' />
    <link rel="icon" href="../images/favicon.ico">
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/bootstrap-datepicker.css" rel="stylesheet">
    <link href="css/sidemenu.css" rel="stylesheet">
    <link href="css/admin.css" rel="stylesheet">
    <link href="css/my-style.css" rel="stylesheet">
    <script src="js/jquery.js"></script>
    <script src="js/functions.js"></script>
    <script src="js/bootstrap.js"></script>
    <script src="js/sidemenu.js"></script>
    <script type="text/javascript" src="js/bootstrap-datepicker.js"></script>

    <script>
    $j = jQuery.noConflict();
    $j(function() {


        $j('#bookingdate').datepicker({
            format: 'yyyy-mm-dd',
            weekStart: 0,
            autoclose: true,
            clearBtn: true,
            todayHighlight: true,
            orientation: 'auto top'
        });
    });
    </script>

    <style>
    .ontop {
        z-index: 999;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        display: none;
        position: fixed;
        background-color: #cccccc;
        color: #aaaaaa;
        opacity: .4;
        filter: alpha(opacity=50);
    }

    #popup {
        width: 300px;
        height: 100px;
        position: absolute;
        color: #000000;
        background-color: #ffffff;
        top: 50%;
        left: 50%;
        margin-top: -100px;
        margin-left: -150px;
    }

    .bolder {
        font-size: 14px;
        font-weight: bold;
    }

    .focus-highlight {
        border: 2px solid #ff0000 !important;
        box-shadow: 0 0 5px rgba(255, 0, 0, 0.5) !important;
    }

    .error-message {
        color: #d9534f;
        font-size: 12px;
        margin-top: 5px;
    }
    </style>


</head>

<body>
    <div id="wrapper">
        <?php
            include 'header_check.php';
            include 'sidemenu' . $_SESSION['type'] . '.php';
            ?>
        <div id="page-content-wrapper">
            <button type="button" class="hamburger is-closed" data-toggle="offcanvas">
                <span class="hamb-top"></span>
                <span class="hamb-middle"></span>
                <span class="hamb-bottom"></span>
            </button>

            <div class="col-lg-8 col-lg-offset-2 table-responsive">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <span class="bolder" style="color: #31708f;">Add New User</span>
                        <a class="pull-right btn btn-danger btn-xs" href="users_list.php">BACK</a>
                    </div>
                    <div class="panel-body">

                        <form class="form-horizontal users_list_form" id="users_list_form" method="post" action=""
                            enctype="multipart/form-data">
                            <!-- Hidden fields for backend compatibility -->
                            <!-- <input type="hidden" name="user_url" value="">
                            <input type="hidden" name="user_type" value="1">
                            <input type="hidden" name="user_restaurant_status" value="1">
                            <input type="hidden" name="activity_status" value="1"> -->
                            <div class="col-md-6">
                                <!-- <div class="form-group">
                                    <label class="control-label col-sm-4">ID</label>
                                    <div class="col-md-6">
                                        <input type="text" name="id" id="id" class="form-control"
                                            value="<?php echo $newid; ?>" readonly>
                                    </div>
                                </div> -->
                                <div class="form-group">
                                    <label class="control-label col-sm-4">User Id *</label>
                                    <div class="col-md-6">
                                        <input type="text" name="user_id" id="user_id" class="form-control numeric"
                                            required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-4">Display Name *</label>
                                    <div class="col-md-6">
                                        <input type="text" name="display_name" id="display_name" class="form-control"
                                            required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-sm-4">User Role *</label>
                                    <div class="col-md-6">
                                        <select name="user_level" id="user_role" class="form-control">
                                            <option value="">--Select--</option>
                                            <option value="0">0</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                            <option value="8">8</option>
                                            <option value="9">9</option>
                                            <option value="10">10</option>
                                        </select>

                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-sm-4">Allowed Packages</label>
                                    <div class="col-md-6">
                                        <input type="text" name="allowed_packages" id="allowed_packages"
                                            class="form-control" value="">
                                        <small class="text-muted">Example: 30001,30002,30003</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-sm-4">Email</label>
                                    <div class="col-md-6">
                                        <input type="email" name="user_email" id="user_email" class="form-control">
                                    </div>
                                </div>
                                <!-- <div class="form-group">
                                    <label class="control-label col-sm-4">URL</label>
                                    <div class="col-md-6">
                                        <input type="text" name="user_url" id="user_url" class="form-control">
                                    </div>
                                </div> -->



                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label col-sm-4"> Assign * </label>
                                    <div class="col-md-6">
                                        <select name="pack_authorization" id="user_assign" class="form-control">
                                            <option value="0">--Select Authorization--</option>
                                            <option value="1">VIP Lounge</option>
                                            <option value="2">Counter</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-4"> Is Client Active </label>
                                    <div class="col-md-6">
                                        <select name="is_client_active" id="is_client_active" class="form-control">
                                            <option value="">--Select--</option>
                                            <option value="1">YES</option>
                                            <option value="0">NO</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-4">Client Role *</label>
                                    <div class="col-md-6">
                                        <select name="user_type" id="user_type" class="form-control">
                                            <option value="U">User</option>
                                            <option value="A">Admin</option>
                                        </select>
                                    </div>
                                </div>
                                <!-- <div class="form-group">
                                    <label class="control-label col-sm-4">User Type *</label>
                                    <div class="col-md-6">
                                        <select name="user_restaurant_status" id="user_restaurant_status"
                                            class="form-control">
                                            <option value="">--Select Type--</option>
                                            <option value="0">Web</option>
                                            <option value="1">Client</option>
                                        </select>
                                    </div>
                                </div> -->

                                <!-- <div class="form-group">
                                    <label class="control-label col-sm-4">User Level</label>
                                    <div class="col-md-6">
                                        <input type="number" name="user_client_level" id="user_client_level"
                                            class="form-control" value="2" required>
                                    </div>
                                </div> -->

                                <div class="form-group">
                                    <label class="control-label col-sm-4"> Password *</label>
                                    <div class="col-md-6">
                                        <input type="password" name="user_pass" id="user_pass" class="form-control"
                                            required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-sm-4"> Re-Type Password *</label>
                                    <div class="col-md-6">
                                        <input type="password" name="retype_user_pass" id="retype_user_pass"
                                            class="form-control" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-4">Status</label>
                                    <div class="col-md-6">

                                        <div style="margin-top:8px;">
                                            <label>
                                                <input type="checkbox" checked disabled> Active
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="col-md-offset-5 col-md-2">
                                        <!-- <input type="submit" name="submit" id="submit_btn" value="Add User"
                                            class="btn btn-success btn-block" style="width:100px;"> -->

                                        <button type="submit" class="btn btn-success mt-4 " style="width:100px;"
                                            id="submit_btn"> Add User </button>
                                    </div>
                                </div>
                            </div>

                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>




    <script>
    // Use jQuery in noConflict mode
    jQuery.noConflict();
    (function($) {
        $(document).ready(function() {

            var specialKeys = [];
            specialKeys.push(8); //Backspace
            $(function() {
                $(".numeric").bind("keypress", function(e) {
                    var keyCode = e.which ? e.which : e.keyCode;
                    var ret = ((keyCode >= 48 && keyCode <= 57) || specialKeys.indexOf(
                            keyCode) != -
                        1);
                    return ret;
                });
            });
            // Form validation on submit
            $('#submit_btn').on('click', function(e) {
                e.preventDefault();

                var isValid = true;
                // Remove the firstErrorField variable

                // Clear previous error messages
                $('.error-message').remove();
                $('.form-control').removeClass('focus-highlight');

                // Validate User ID
                var userId = $('#user_id').val().trim();
                if (userId === '') {
                    showError('#user_id', 'User ID is required.');
                    isValid = false;
                } else if (userId.length < 3) {
                    showError('#user_id', 'User ID must be at least 3 characters long.');
                    isValid = false;
                } else {
                    // Check if user ID exists
                    $.ajax({
                        url: 'users_insert.php',
                        type: 'POST',
                        data: {
                            check_username: userId
                        },
                        dataType: 'json',
                        async: false, // Make synchronous request to ensure validation completes before form submission
                        success: function(response) {
                            if (response.exists) {
                                showError('#user_id',
                                    'This User Id already exists. Please choose a different one.'
                                );
                                isValid = false;
                            }
                        }
                    });
                }

                // Validate Display Name
                var displayName = $('#display_name').val().trim();
                if (displayName === '') {
                    showError('#display_name', 'Display Name is required.');
                    isValid = false;
                } else if (displayName.length < 2) {
                    showError('#display_name', 'Display Name must be at least 2 characters long.');
                    isValid = false;
                }

                // Validate User Role
                var userRole = $('#user_role').val();
                if (userRole === '' || userRole === null) {
                    showError('#user_role', 'User Role is required.');
                    isValid = false;
                }

                // Validate User Assign
                var userAssign = $('#user_assign').val();
                if (userAssign === '0' || userAssign === '' || userAssign === null) {
                    showError('#user_assign', 'Please select an assignment.');
                    isValid = false;
                }



                // Validate User Assign
                var is_client_active = $('#is_client_active').val();
                if (is_client_active === '' || is_client_active === null) {
                    showError('#is_client_active', 'Please select an assignment.');
                    isValid = false;
                }

                // Validate Email (if provided)
                var userEmail = $('#user_email').val().trim();
                if (userEmail !== '') {
                    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(userEmail)) {
                        showError('#user_email', 'Please enter a valid email address.');
                        isValid = false;
                    }
                }

                // Inside the submit_btn click handler, add this validation
                var allowedPackages = $('#allowed_packages').val().trim();
                if (allowedPackages !== '') {
                    // Check if the format is valid (only numbers and commas)
                    if (!/^[0-9]+(,[0-9]+)*$/.test(allowedPackages)) {
                        showError('#allowed_packages',
                            'Please enter only numbers separated by commas.');
                        isValid = false;
                    }
                }

                // Validate Password
                var password = $('#user_pass').val();
                if (password === '') {
                    showError('#user_pass', 'Password is required.');
                    isValid = false;
                } else if (password.length < 6) {
                    showError('#user_pass', 'Password must be at least 6 characters long.');
                    isValid = false;
                }

                // Validate Re-type Password
                var retypePassword = $('#retype_user_pass').val();
                if (retypePassword === '') {
                    showError('#retype_user_pass', 'Please re-type the password.');
                    isValid = false;
                } else if (password !== retypePassword) {
                    showError('#retype_user_pass', 'Passwords do not match.');
                    isValid = false;
                }

                // If validation fails, stop form submission
                if (!isValid) {
                    return false;
                }

                // If all validations pass, show confirmation
                var confirmMessage = 'Are you sure you want to add this user?\n\n' +
                    'User ID: ' + userId + '\n' +
                    'Display Name: ' + displayName + '\n' +
                    'User Role: ' + userRole + '\n' +
                    'Assignment: ' + $('#user_assign option:selected').text();

                var confirm = window.confirm(confirmMessage);

                if (confirm) {
                    // Add a hidden field to indicate this is a form submission
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'submit_user',
                        value: '1'
                    }).appendTo('#users_list_form');

                    // Set the form attributes and submit
                    var form = document.getElementById('users_list_form');
                    form.setAttribute('method', 'POST');
                    form.setAttribute('action', 'users_insert.php');
                    form.setAttribute('enctype', 'multipart/form-data');
                    form.setAttribute('target', '_blank');

                    // Use this approach to avoid the "submit is not a function" error
                    var hiddenButton = document.createElement('input');
                    hiddenButton.type = 'submit';
                    hiddenButton.style.display = 'none';
                    form.appendChild(hiddenButton);
                    hiddenButton.click();
                    form.removeChild(hiddenButton);
                }
                // if (confirm) {
                //     // Set the form attributes and submit directly to users_insert.php
                //     var form = document.getElementById('users_list_form');
                //     form.setAttribute('method', 'POST');
                //     form.setAttribute('action', 'users_insert.php');
                //     form.setAttribute('enctype', 'multipart/form-data');
                //     form.setAttribute('target', '_blank');
                //     form.submit();
                // }
                // if (confirm) {
                //     // Submit the form
                //     $('#users_list_form').submit();
                // }
            });


            //     // If all validations pass, show confirmation
            //     var confirmMessage = 'Are you sure you want to add this user?\n\n' +
            //         'User ID: ' + userId + '\n' +
            //         'Display Name: ' + displayName + '\n' +
            //         'User Role: ' + userRole + '\n' +
            //         'Assignment: ' + $('#user_assign option:selected').text();

            //     var confirm = window.confirm(confirmMessage);
            //     if (confirm) {
            //         // Submit the form
            //         $('#users_list_form').submit();
            //     }
            // });

            // Function to show error message
            // function showError(fieldId, message) {
            //     var $field = $(fieldId);
            //     $field.addClass('focus-highlight');
            //     var $errorDiv = $('<div class="error-message">' + message + '</div>');
            //     $field.closest('.form-group').find('.col-md-6').append($errorDiv);
            // }


            // Function to show error message
            function showError(fieldId, message) {
                var $field = $(fieldId);
                // Add focus-highlight class to the field
                $field.addClass('focus-highlight');
                // Create error message div
                var $errorDiv = $('<div class="error-message">' + message + '</div>');
                // Append error message to the parent container
                $field.closest('.form-group').find('.col-md-6').append($errorDiv);
                // Make sure error message is visible
                $errorDiv.show();
            }


            // Real-time validation for User ID
            $('#user_id').on('blur', function() {
                var userId = $(this).val().trim();
                if (userId !== '') {
                    // Check if username already exists via AJAX
                    $.ajax({
                        url: 'users_insert.php',
                        type: 'POST',
                        data: {
                            check_username: userId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.exists) {
                                showError('#user_id',
                                    'This User Id already exists. Please choose a different one.'
                                );
                            }
                        },
                        error: function() {
                            // Silently fail - server-side validation will catch duplicates
                        }
                    });
                }
            });

            // Clear error styling on focus
            $('.form-control').on('focus', function() {
                $(this).removeClass('focus-highlight');
                $(this).closest('.form-group').find('.error-message').remove();
            });

            // Validation for allowed_packages field
            $("#allowed_packages").on("keypress", function(e) {
                var keyCode = e.which ? e.which : e.keyCode;
                var currentValue = $(this).val();

                // Allow digits (48-57)
                if (keyCode >= 48 && keyCode <= 57) {
                    return true;
                }

                // Allow comma (44) only if:
                // 1. The current value is not empty
                // 2. The last character is not a comma
                // 3. The current value has at least one digit
                if (keyCode === 44) {
                    if (currentValue.length === 0) {
                        e.preventDefault();
                        return false;
                    }

                    if (currentValue.slice(-1) === ',') {
                        e.preventDefault();
                        return false;
                    }

                    // Check if there are at least 3 digits before allowing a comma
                    var lastCommaIndex = currentValue.lastIndexOf(',');
                    var segmentToCheck = lastCommaIndex === -1 ?
                        currentValue :
                        currentValue.substring(lastCommaIndex + 1);

                    if (segmentToCheck.length < 3) {
                        e.preventDefault();
                        return false;
                    }

                    return true;
                }

                // Block all other characters
                e.preventDefault();
                return false;
            });
            // Password strength indicator
            // $('#user_pass').on('input', function() {
            //     var password = $(this).val();
            //     var strength = getPasswordStrength(password);

            //     // Remove existing strength indicator
            //     $(this).closest('.form-group').find('.password-strength').remove();

            //     if (password.length > 0) {
            //         var strengthText = '';
            //         var strengthClass = '';

            //         switch (strength) {
            //             case 1:
            //                 strengthText = 'Weak';
            //                 strengthClass = 'text-danger';
            //                 break;
            //             case 2:
            //                 strengthText = 'Fair';
            //                 strengthClass = 'text-warning';
            //                 break;
            //             case 3:
            //                 strengthText = 'Good';
            //                 strengthClass = 'text-info';
            //                 break;
            //             case 4:
            //                 strengthText = 'Strong';
            //                 strengthClass = 'text-success';
            //                 break;
            //         }

            //         var $strengthDiv = $('<div class="password-strength ' + strengthClass +
            //             '" style="font-size: 12px; margin-top: 5px;">Password Strength: ' +
            //             strengthText + '</div>');
            //         $(this).closest('.form-group').find('.col-md-6').append($strengthDiv);
            //     }
            // });

            // Function to calculate password strength
            // function getPasswordStrength(password) {
            //     var strength = 0;

            //     if (password.length >= 6) strength++;
            //     if (password.length >= 8) strength++;
            //     if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
            //     if (/\d/.test(password)) strength++;
            //     if (/[^a-zA-Z0-9]/.test(password)) strength++;

            //     return Math.min(strength, 4);
            // }
        });
    })(jQuery);
    </script>
</body>

</html>