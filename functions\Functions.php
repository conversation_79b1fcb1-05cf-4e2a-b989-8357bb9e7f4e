<?php

session_start();

//error_reporting(E_ALL & ~E_WARNING & ~E_NOTICE);

$user = $_SESSION['user'];

//$remoteclientip = $_SESSION['remoteclientip'];

function fnget_ticketscount($ticket_orderno) {

    include '../dbconnect.php';
    global $conn;
    global $user;

    $ex_type = substr($ticket_orderno, 11, 3);
    $ex_type = strtoupper($ex_type);
    $tickettype = 1;
    if ($ex_type == 'RTA' || $ex_type == 'RCC') {
        $tickettype = 2;
    }

    $table_end = date('Ym');
    $table_end_online = date('Ym') . '_online';

    if ($tickettype == 1) {//regular saving
        $headertbl = 'rfc_tickets_booking_header_' . $table_end . '';
        $detailstbl = 'rfc_tickets_booking_details_' . $table_end . '';
        $detailstbl_online = 'rfc_tickets_booking_details_' . $table_end_online . '';
        $cat_detailstbl = 'rfc_tickets_booking_category_details_' . $table_end . '';
        $paymenttbl = 'rfc_tickets_booking_payment_details_' . $table_end . '';
    } elseif ($tickettype == 2) {//grouping saving
        $headertbl = 'rfc_tickets_booking_group_header_' . $table_end . '';
        $detailstbl = 'rfc_tickets_booking_group_details_' . $table_end . '';
        $detailstbl_online = 'rfc_tickets_booking_group_details_' . $table_end_online . '';
        $cat_detailstbl = 'rfc_tickets_booking_group_category_details_' . $table_end . '';
        $paymenttbl = 'rfc_tickets_booking_group_payment_details_' . $table_end . '';
    }
    $scan_count = 0;
    $cancel_count = 0;
    $adult = 0;
    $child = 0;
    $couple = 0;
    $total = 0;
    $found = 0;
    $package_name = '';
    $message = '';
    $today = date('Y-m-d');
    $today_ymd = date('Ymd');
//    $today = "2023-01-17";
//    $today_ymd = "20230117";

    $query = " Select * from rfc_tickets inner join rfc_packages using(package_id) where order_no='$ticket_orderno'";
    $rs5 = $conn->prepare($query);
    $rs5->execute();
    if ($rs5->rowCount() > 0) {
        while ($row5 = $rs5->fetchObject()) {
            if ($row5->master_id != 2) {
                $message = 'Invalid Ticket No. Please check';
                echo $message . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
                exit;
            } else {

            }
            $tickets_visitdate = date("Ymd", strtotime($row5->visitdate));
            if ($today_ymd != $tickets_visitdate) {
                $message = 'Invalid Ticket ( VisitDate Issue ). Please check';
                echo $message . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
                exit;
            }
            if ($row5->status != 'PAYMENT_MADE') {
                $message = 'Invalid Ticket (Ticket payment not yet Completed). Please cross check';
                echo $message . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
                exit;
            }
        }
    } else {
        $message = 'Invalid Ticket No. Please cross check';
        echo $message . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
        exit;
    }

    $query = " Select count(*) as tickets_count,";
    $query .= " sum(case when qr_isscanned=1 then 1 else 0 end) as scan_count,";
    $query .= " sum(case when cancel_flag=1 then 1 else 0 end) as cancel_count";
    $query .= " from rfc_tickets_booking_qr_details where order_no='$ticket_orderno'";

//    echo $query . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
//    exit;

    $rs2 = $conn->prepare($query);
    $rs2->execute();

//    echo $booking_qr->rowCount() . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
//    exit;
    //echo pg_num_rows($rs2) . '</br>';
    //echo $rs2->rowCount();
    //exit;

    if ($rs2->rowCount() > 0) {
//        echo '1' . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
//        exit;

        while ($row2 = $rs2->fetchObject()) {
            //echo $row2->scan_count . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
            //exit;

            $scan_count = $row2->scan_count;
            $cancel_count = $row2->cancel_count;
        }
    }

    if ($scan_count > 0) {
        $query = " Select * from rfc_tickets_booking_qr_details where order_no='$ticket_orderno'";
        $rs3 = $conn->prepare($query);
        $rs3->execute();
        if ($rs3->rowCount() > 0) {
            while ($row3 = $rs3->fetchObject()) {
                if ($row3->qr_isscanned == 1) {
                    //$scan_info = 'Ticket Allready Scanned on ' . $row3->qr_scanneddate . ' by ' . $row3->qr_scannedby;
                    $scan_info = 'Ticket Allready Scanned by ' . $row3->qr_scannedby . ' at ' . $row3->qr_scanneddate;
                }
            }
        }
        //tickets category wise tickets count
        $query = " Select * from $detailstbl_online";
        $query .= " inner join rfc_packages on det002_package_code=package_id";
        $query .= " where det002_order_no='$ticket_orderno'";
        $query .= " order by det002_ticket_category,det002_ticket_category";

        $rs4 = $conn->prepare($query);
        $rs4->execute();
        if ($rs4->rowCount() > 0) {
            while ($row4 = $rs4->fetchObject()) {

                //$package_name = $row1->title;

                if ($row4->det002_ticket_category == 1) {
                    $adult += 1;
                } elseif ($row4->det002_ticket_category == 2) {
                    $child += 1;
                } elseif ($row4->det002_ticket_category == 3) {
                    $couple += 1;
                }
            }
            $total = $adult + $child + $couple;
        }

        $message = $scan_info;
        echo $message . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
        exit;
    }


    if ($cancel_count > 0) {
        $query = " Select * from $detailstbl";
        $query .= " inner join rfc_packages on det002_package_code=package_id";
        $query .= " where det002_order_no='$ticket_orderno'";
        $query .= " order by det002_ticket_category,det002_ticket_category";

//        echo $query . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
//        exit;
        $rs1 = $conn->prepare($query);
        $rs1->execute();
        $tkt_count = 0;
        $tkt_cancel_count = 0;
        if ($rs2->rowCount() > 0) {
            while ($row1 = $rs1->fetchObject()) {
                $message = "Ticket Cancelled and Taken Tickets from Counter No : " . $row1->det002_counter_id . "";
                $message = strtoupper($message);

                $package_name = $row1->title;

                if ($row1->det002_ticket_category == 1) {
                    $adult += 1;
                } elseif ($row1->det002_ticket_category == 2) {
                    $child += 1;
                } elseif ($row1->det002_ticket_category == 3) {
                    $couple += 1;
                }


                if ($row1->det002_scannedby != 0) {
                    //$scan_info = $row1->det002_scannedby . ' on ' . $row1->det002_scanneddate;
                }
                //$tkt_count += 1;
                if ($row1->det002_cancel_flag == 1) {
                    //$tkt_cancel_count += 1;
                }
            }
            $total = $adult + $child + $couple;
        }
        echo $message . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
        exit;
    } else if ($scan_count > 0) {
        //$message = $scan_info;
        //echo $message . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
        //exit;
    } else {
        //echo '5' . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
        //exit;
        $message = '';
        $query = " Select det002_ticket_category,title,catg_name,det002_order_no,count(*) as ticket_count from $detailstbl_online";
        $query .= " inner join rfc_tickets_category on catg_id = det002_ticket_category";
        $query .= " inner join rfc_packages on det002_package_code=package_id";
        $query .= " where det002_order_no='$ticket_orderno'";
        $query .= " and det002_isscanned=0 and det002_scannedby=0 and det002_scanneddate is null";
        $query .= " and det002_ticket_category in (1,2)";
        $query .= " group by 1,2,3,4 order by 1";

//        echo $query . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
//        exit;

        $rs = $conn->prepare($query);
        $rs->execute();
        while ($row = $rs->fetchObject()) {

            $found = 0;

            if ($row->det002_ticket_category == 1) {
                $adult = $row->ticket_count;
            } elseif ($row->det002_ticket_category == 2) {
                $child = $row->ticket_count;
            } elseif ($row->det002_ticket_category == 3) {
                $couple = $row->ticket_count;
            }

            $package_name = $row->title;
            $message = '';
        }

        $total = $adult + $child + $couple;

        //$ticket_orderno = $ticket_orderno . '[Package Name : ' . $package_name . ']';

        echo $message . '~' . $adult . '~' . $child . '~' . $couple . '~' . $total . '~' . $package_name . '~' . $ticket_orderno;
        exit;
    }
}

function fnSaveOrderDetailsToScanning($ticket_order, $ticket_date) {

    include ' . ./dbconnect . php';
    global $conn;
    global $user;

//    echo $user;
//    exit;

    $query = " Select * from rfc_tickets_booking_qr_details where order_no='$ticket_order'";
    $query .= " and ticket_date='$ticket_date'";
    $query .= " and cancel_flag=0";
    $query .= " and qr_isscanned=0";
    $query .= " order by sequence_no";

//    echo $query;
//    exit;

    $booking_qr = $conn->prepare($query);
    $booking_qr->execute();

    $ticket = '';
    //$today = date('Y-m-d');
    $today = $ticket_date;

    if (strlen($user) <= 0) {
        $user = '9317380';
    }

    $ym = date('Ym');
    while ($qr_data = $booking_qr->fetchObject()) {

        $ticket = $qr_data->tickettext;

        $queryscan = "insert into rfc_thirdpartyticketlog (ticklog_tktno,ticklog_scanfrom, ticklog_createdby ) values('$ticket', 'web', '$user')";
        $resultscan = $conn->prepare($queryscan);
        $resultscan->execute();

        $qr_sec = "select ticket_no, emp_code, emp_name, counter_id, public_autosync, rfc_tickets_booking_qr_details.order_no,pending_amount,with_food,qr_package_id  from rfc_tickets_booking_qr_details ";
        $qr_sec .= " inner join rfc_tickets on rfc_tickets_booking_qr_details.order_no=rfc_tickets.order_no ";
        $qr_sec .= " where ticket_date = '$today'";
        $qr_sec .= "  and tickettext = '$ticket' and rfc_tickets_booking_qr_details.cancel_flag=0 ";
        $qr_sec .= "  and qr_masterid=2";
        $qr_sec .= "  and with_food=0";
        $qr_sec .= "  and qr_package_id not in (30309,30308,30314,30315,30321,30339,30368)";
        $qr_sec .= " and pending_amount=0";

//        echo $qr_sec;
//        exit;

        $rs_sec = $conn->prepare($qr_sec);
        $rs_sec->execute();
        $cnt_sec = $rs_sec->rowCount();

        if ($cnt_sec == 1) {

            $rw_sec = $rs_sec->fetchObject();

            if ($rw_sec->qr_package_id == 30391 || $rw_sec->qr_package_id == 30392) {
//                if (date('H') < 13) {
//                    header("HTTP/1.1 403 Bad Request");
//                    echo "0";
//                    exit;
//                }
            }

            $online_counterid = $rw_sec->counter_id;
            $online_ticketno = $rw_sec->ticket_no;
            $online_autosync = $rw_sec->public_autosync;

            $online_orderno = $rw_sec->order_no;
            $online_adult_count = 0;
            $online_child_count = 0;
            $online_couple_count = 0;
            $online_updby = "ONLINE-USER";

            $qr_sec_ins = "insert into rfc_ticket_security_log (log_ticket_no, log_entry_by) values($rw_sec->ticket_no, '$user')";
            $rs_sec_ins = $conn->prepare($qr_sec_ins);
            $rs_sec_ins->execute();

            if ($online_autosync == 1) {

                $query = " Update rfc_tickets_booking_qr_details set qr_isscanned=1,qr_scannedby='$user',qr_scanneddate = now()";
                $query .= " where ticket_date = '$today'";
                $query .= "  and order_no='" . $online_orderno . "'";
                $query .= "  and tickettext = '$ticket'";

                $qr_scan = $conn->prepare($query);
                $qr_scan->execute();
            }



            if ($online_autosync == 1) {

                $querybooked1 = "select det002_tickettext, det002_isscanned, det002_ticket_no,det002_scanneddate,det002_ticket_category from rfc_tickets_booking_details_" . $ym . "_online where det002_ticket_date = '$today' and det002_cancel_flag=0";
                $querybooked1 .= " and det002_tickettext ='$ticket'";

                $resultbooked1 = $conn->prepare($querybooked1);
                $resultbooked1->execute();
                $cntbooked1 = $resultbooked1->rowCount();
                if ($cntbooked1 == 0) {
                    $querybooked = "select det002_tickettext , det002_isscanned , det002_ticket_no , det002_scanneddate,det002_ticket_category from rfc_tickets_booking_group_details_" . $ym . "_online where det002_ticket_date = '$today' and det002_cancel_flag=0";
                    $querybooked .= " and det002_tickettext='$ticket'";
                    //$querybooked .= " and rfc001_isscanned=0 ";

                    $resultbooked = $conn->prepare($querybooked);
                    $resultbooked->execute();
                    $cntbooked = $resultbooked->rowCount();
                    if ($cntbooked == 0) {
                        //header("HTTP/1.1 403 Bad Request");
                        //echo "0";
                        //exit;
                    } else {
                        $rowbooked = $resultbooked->fetchObject();
                        $scanned = $rowbooked->det002_isscanned;

                        if ($rowbooked->det002_ticket_category == 1) {
                            $online_adult_count = 1;
                        }
                        if ($rowbooked->det002_ticket_category == 2) {
                            $online_child_count = 1;
                        }
                        if ($rowbooked->det002_ticket_category == 5) {
                            $online_couple_count = 1;
                        }


                        if ($scanned == 1) {
                            //header("HTTP/1.1 403 Bad Request");
                            //echo "0";
                            //exit;
                        } else {
                            $queryup = "update rfc_tickets_booking_group_details_" . $ym . "_online set det002_scanneddate = now(), det002_isscanned=1 , det002_scannedby ='$user' where det002_ticket_no = $rowbooked->det002_ticket_no and det002_ticket_date = '$today' ";
                            $queryup .= " and det002_tickettext='$ticket'";
                            $resultup = $conn->prepare($queryup);
                            $resultup->execute();

                            $queryup = "update rfc_tickets_booking_group_details_" . $ym . " set det002_scanneddate = now(), det002_isscanned=1 , det002_scannedby ='$user' where det002_ticket_no = $rowbooked->det002_ticket_no and det002_ticket_date = '$today' ";
                            $queryup .= " and det002_tickettext='$ticket'";
                            $resultup = $conn->prepare($queryup);
                            $resultup->execute();

                            $order_no = $online_orderno;
                            $adult = $online_adult_count;
                            $child = $online_child_count;
                            $couple = 0;
                            $resultArr = array();
                            $qrindex = 0;

                            $resultArr[$qrindex] = $ticket;

                            $responsejson = json_encode($resultArr);

                            $upd_url = 'https://rfcadmin.ramojifilmcity.com/daytourcms/ticket_issue_update.php';
                            $upd_url .= "?order_no=$online_orderno";
                            $upd_url .= "&visitdate=$today";
                            $upd_url .= "&adult=$online_adult_count";
                            $upd_url .= "&child=$online_child_count";
                            $upd_url .= "&couple=$online_couple_count";
                            $upd_url .= "&updby=$online_updby";
                            $upd_url .= "&qrarray=$responsejson";

                            $upd_res = file_get_contents($upd_url);

                            $ticketno_ins = $rowbooked->det002_ticket_no;
//                            echo "1";
//                            exit;
                        }
                    }
                } else {
                    $rowbooked1 = $resultbooked1->fetchObject();
                    $scanned1 = $rowbooked1->det002_isscanned;

                    if ($rowbooked1->det002_ticket_category == 1) {
                        $online_adult_count = 1;
                    }
                    if ($rowbooked1->det002_ticket_category == 2) {
                        $online_child_count = 1;
                    }
                    if ($rowbooked1->det002_ticket_category == 5) {
                        $online_couple_count = 1;
                    }

                    if ($scanned1 == 1) {
                        //header("HTTP/1.1 403 Bad Request");
                        //echo "0";
                        //exit;
                    } else {
                        $queryup = "update rfc_tickets_booking_details_" . $ym . "_online set det002_scanneddate = now(), det002_isscanned=1 , det002_scannedby ='$user' where det002_ticket_no = $rowbooked1->det002_ticket_no and det002_ticket_date = '$today' ";
                        $queryup .= " and det002_tickettext='$ticket'";

//                       echo $queryup;
//                       exit;
                        $resultup = $conn->prepare($queryup);
                        $resultup->execute();

                        $queryup = "update rfc_tickets_booking_details_" . $ym . " set det002_scanneddate = now(), det002_isscanned=1 , det002_scannedby ='$user' where det002_ticket_no = $rowbooked1->det002_ticket_no and det002_ticket_date = '$today' ";
                        $queryup .= " and det002_tickettext='$ticket'";

                        //echo $queryup;
                        //exit;

                        $resultup = $conn->prepare($queryup);
                        $resultup->execute();

                        $order_no = $online_orderno;
                        $adult = $online_adult_count;
                        $child = $online_child_count;
                        $couple = 0;
                        $resultArr = array();
                        $qrindex = 0;

                        $resultArr[$qrindex] = $ticket;
                        $responsejson = json_encode($resultArr);

                        $upd_url = 'https://rfcadmin.ramojifilmcity.com/daytourcms/ticket_issue_update.php';
                        $upd_url .= "?order_no=$online_orderno";
                        $upd_url .= "&visitdate=$today";
                        $upd_url .= "&adult=$online_adult_count";
                        $upd_url .= "&child=$online_child_count";
                        $upd_url .= "&couple=$online_couple_count";
                        $upd_url .= "&updby=$online_updby";
                        $upd_url .= "&qrarray=$responsejson";

                        $upd_res = file_get_contents($upd_url);

                        $ticketno_ins = $rowbooked1->det002_ticket_no;
//                        echo "1";
//                        exit;
                    }
                }
            }
        }
    }
    echo "1";
//    exit;
}
