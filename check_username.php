<?php
session_start();
if (!isset($_SESSION['user'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

include './dbconnect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['username'])) {
    $username = trim($_POST['username']);
    
    if (empty($username)) {
        echo json_encode(['exists' => false]);
        exit;
    }
    
    try {
        $check = $conn->prepare("SELECT COUNT(*) FROM rfc_user_login WHERE user_name = ?");
        $check->execute([$username]);
        $count = $check->fetchColumn();
        
        echo json_encode(['exists' => $count > 0]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error']);
    }
} else {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid request']);
}
?>
