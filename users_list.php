<?php
session_start();
if (!isset($_SESSION['user'])) {
    $_SESSION['logerr'] = "You have been logged out. Try Again.";
    header("location:index.php");
}
include "dbconnect.php";
if (isset($_POST['submit'])) {
    $fdate = $_REQUEST['bookingdate'];
} else {
    $fdate = date('Y-m-d');
}

$radio = "";
$allchk = "";
$webchk = "";
$offchk = "";
$assign = "";
if (isset($_POST['submit'])) {
                        if (isset($_POST['user_type'])) {
                            if ($_POST['user_type'] == "all") {
                                $radio = "all";
                                $allchk = "checked";
                            } elseif ($_POST['user_type'] == "active") {
                                $radio = "active";
                                $webchk = "checked";
                            } elseif ($_POST['user_type'] == "suspended") {
                                $radio = "suspended";
                                $offchk = "checked";
                            } else {
                                $radio = "";
                            }
                            if (isset($_POST['assign'])) {
                                $assign = $_POST['assign'];
                            }
                            if (isset($_POST['issue'])) {
                                $issue = $_POST['issue'];
                            }
                        }
}
?>

<html>

<head>

    <link href="datatables/ie10-viewport-bug-workaround.css" rel="stylesheet">
    <script src="datatables/ie-emulation-modes-warning.js"></script>

    <link href="datatables/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="datatables/dataTables.bootstrap4.min.css" />
    <link rel="stylesheet" href="datatables/buttons.bootstrap4.min.css" />


    <title>Ramoji Film City :: Admin</title>
    <meta name='robots' content='nofollow,noindex' />
    <link rel="icon" href="../images/favicon.ico">
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/bootstrap-datepicker.css" rel="stylesheet">
    <link href="css/sidemenu.css" rel="stylesheet">
    <link href="css/admin.css" rel="stylesheet">
    <link href="css/my-style.css" rel="stylesheet">
    <script src="js/jquery.js"></script>
    <script src="js/functions.js"></script>
    <script src="js/bootstrap.js"></script>
    <script src="js/sidemenu.js"></script>
    <script type="text/javascript" src="js/bootstrap-datepicker.js"></script>

    <script>
    $j = jQuery.noConflict();
    $j(function() {


        $j('#bookingdate').datepicker({
            format: 'yyyy-mm-dd',
            weekStart: 0,
            autoclose: true,
            clearBtn: true,
            todayHighlight: true,
            orientation: 'auto top'
        });
    });
    </script>

    <style>
    .ontop {
        z-index: 999;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        display: none;
        position: fixed;
        background-color: #cccccc;
        color: #aaaaaa;
        opacity: .4;
        filter: alpha(opacity=50);
    }

    #popup {
        width: 300px;
        height: 100px;
        position: absolute;
        color: #000000;
        background-color: #ffffff;
        top: 50%;
        left: 50%;
        margin-top: -100px;
        margin-left: -150px;
    }
    </style>
</head>

<body>
    <div id="wrapper">
        <?php
            include 'header_check.php';
            include 'sidemenu' . $_SESSION['type'] . '.php';
            ?>
        <div id="page-content-wrapper">
            <button type="button" class="hamburger is-closed" data-toggle="offcanvas">
                <span class="hamb-top"></span>
                <span class="hamb-middle"></span>
                <span class="hamb-bottom"></span>
            </button>

            <div id="popDiv" class="ontop">
                <table border="1" id="popup">
                    <tr>
                        <td style="text-align: center;">Please Wait Loading...</td>
                    </tr>
                </table>
            </div>


            <div class="col-lg-10 col-lg-offset-1">

                <div class="col-md-12">


                    <div class="col-sm-12" style="border: 1px solid black;padding: 0;margin-bottom: 29px;">

                        <form action="" method="post">
                            <h3 class="bg-primary" style="padding:10px !important;margin-top: 0px;">USERS LIST
                            </h3>
                            <div style="padding:10px;">
                                <!-- <div class="col-sm-2">
                                    <div class="form-group">
                                        <label for="exampleInputEmail1">Booking Date</label>
                                        <input class="form-control" id="bookingdate" name="bookingdate"
                                            placeholder="dd/mm/yyyy" type="text" value="<?php echo $fdate; ?>" />
                                    </div>
                                </div> -->


                                <!-- <div class="col-sm-2"
                                    style="margin-top: 26px; border: 1px solid #b1b1b1d6;padding: 4px 10px;border-radius: 5px;">
                                    <label class="radio-inline">
                                        <input type="radio" id="all" name="user_type" value="all"
                                            <?php echo $allchk; ?> checked>All
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" id="website" name="user_type" value="website"
                                            <?php echo $webchk; ?>>Website
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" id="ofline" name="user_type" value="offline"
                                            <?php echo $offchk; ?>>Offline
                                    </label>
                                </div> -->

                                <div class="col-sm-3">
                                    <div class="form-group" style="margin-bottom: 0px;">
                                        <label for="user_type_select">Select Type:</label>

                                    </div>
                                    <div class=""
                                        style=" border: 1px solid #b1b1b1d6;padding: 4px 10px;border-radius: 5px;">
                                        <label class="radio-inline" style="padding-right: 10px;">
                                            <input type="radio" id="all" name="user_type" value="all"
                                                <?php echo $allchk; ?> checked>All
                                        </label>
                                        <label class="radio-inline" style="padding-right: 10px;">
                                            <input type="radio" id="active" name="user_type" value="active"
                                                <?php echo $webchk; ?>>Active
                                        </label>
                                        <label class="radio-inline" style="padding-right: 10px;">
                                            <input type="radio" id="suspended" name="user_type" value="suspended"
                                                <?php echo $offchk; ?>>Suspended
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <div class="form-group">
                                        <label for="sel1">Assign:</label>
                                        <select id="assign" name="assign" class="form-control">
                                            <?php if ($assign == 1) { ?>
                                            <option value="1" selected> Web User </option>
                                            <option value="2"> Client User </option>
                                            <?php } else { ?>
                                            <option value="1" selected> Web User </option>
                                            <option value="2"> Client User </option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>

                                <div class="col sm-3" style="margin-top: 24px;">

                                    <input type="submit" class="btn btn-success submitbtn" id="submitbtn" name="submit"
                                        value="Get Data">
                                    &nbsp;
                                    &nbsp;
                                    <a href="users_add.php" class="btn btn-success">Add New User </a>
                                </div>

                            </div>
                        </form>
                    </div>





                    <?php
                    if (isset($_POST['submit'])) {

                        if (isset($_POST['user_type'])) {
                            if ($_POST['user_type'] == "all") {
                                $radio = "all";
                                $allchk = "checked";
                            } elseif ($_POST['user_type'] == "active") {
                                $radio = "active";
                                $webchk = "checked";
                            } elseif ($_POST['user_type'] == "suspended") {
                                $radio = "suspended";
                                $offchk = "checked";
                            } else {
                                $radio = "";
                            }
                            if (isset($_POST['assign'])) {
                                $assign = $_POST['assign'];
                            }
                            if (isset($_POST['issue'])) {
                                $issue = $_POST['issue'];
                            }
                        }
                        //    echo $assign;
                        //    exit;
                        //    print_r($_POST);exit;
                        ?>
                    <div class="col-md-12" style="overflow-y:scroll; overflow-x:scroll; height:697px;">
                        <table class="table table-hovered table-bordered table-striped" id="example">
                            <thead style="position:sticky;top: -5px;background: #337ab7;color: white;">
                                <tr>
                                    <th>SL.No</th>
                                    <th>ACTION</th>
                                    <th>User Id</th>
                                    <th>User Name</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Assign</th>
                                    <th>Eligible Packages</th>
                                    <th>Entry Details</th>
                                    <th>Last Modified</th>
                                    <!-- <th>User Level</th>
                                    <th>User Type</th>
                                    <th>Client Level</th> -->
                                </tr>
                            </thead>
                            <tbody id="rep">
                                <?php
                                    // Build query based on POST data
                                    $where = [];
                                    $params = [];

                                    // Filter by fav_language (activity_status)
                                    if (isset($_POST['user_type'])) {
                                        if ($_POST['user_type'] == "all") {
                                        } elseif ($_POST['user_type'] == "active") {
                                            $where[] = "activity_status = 1";
                                        } elseif ($_POST['user_type'] == "suspended") {
                                            $where[] = "activity_status = 0";
                                        }
                                        if (isset($_POST['assign'])) {
                                            $assign = $_POST['assign'];
                                        }
                                        if (isset($_POST['issue'])) {
                                            $issue = $_POST['issue'];
                                        }
                                    }

                                    // Filter by assign (user_client_level)
                                    if (isset($_POST['assign'])) {
                                        // $where[] = "user_client_level = ?";
                                        $params[] = $_POST['assign'];
                                    }

                                    $query = "SELECT * FROM rfc_user_login";
                                    if (count($where) > 0) {
                                        $query .= " WHERE " . implode(" AND ", $where);
                                    }
                                    $query .= " ORDER BY ID ASC";

                                    // echo $query;exit;

                                    $result = $conn->prepare($query);
                                    $result->execute($params);

                                    $i = 0;
                                    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                                        $i++;
                                        $status = ($row['activity_status'] == 1) ? 'Active' : 'Suspended';

                                        $assign = '';
                                        if ($row['pack_authorization'] == 1) {
                                            $assign = 'VIP Lounge';
                                        } elseif ($row['pack_authorization'] == 2) {
                                            $assign = 'Counter';
                                        } else {
                                            $assign = '';
                                        }

                                        echo "<tr>";
                                        echo "<td>{$i}</td>";
                                        echo "<td><a href='user_edit.php?id={$row['ID']}' class='btn btn-warning'>EDIT</a></td>";
                                        echo "<td>{$row['user_name']}</td>";
                                        echo "<td>{$row['display_name']}</td>";
                                        echo "<td>{$row['user_level']}</td>";
                                        echo "<td>{$status}</td>";
                                        echo "<td>{$assign}</td>";
                                       
                                        echo "<td>{$row['allowed_packages']}</td>";
                                        echo "<td>{$row['registered_by']}<br>{$row['registered_date']}</td>";
                                        echo "<td>{$row['modify_by']}<br>{$row['modify_date']}</td>";
                                        echo "</tr>";
                                    }
                                    ?>
                            </tbody>
                        </table>
                    </div>
                    <div style="border:1px"></div>
                    <?php } ?>





                </div>
            </div>
        </div>


        <script type="text/javascript">
        (function($) {
            $(document).ready(function() {
                $("#submitbtn").on("click", function(e) {
                    //            alert('test');
                });
            });
        })(jQuery);
        </script>

        <script src="datatables/Bootstrap.min.js"></script>
        <!--data tables-->

        <script src="datatables/jquery.dataTables.min.js"></script>
        <script src="datatables/dataTables.bootstrap.min.js"></script>
        <script src="datatables/dataTables.buttons.min.js"></script>
        <script src="datatables/buttons.bootstrap.min.js"></script>
        <script src="datatables/jszip.min.js"></script>
        <script src="datatables/pdfmake.min.js"></script>
        <script src="datatables/vfs_fonts.js"></script>
        <script src="datatables/buttons.html5.min.js"></script>
        <script src="datatables/buttons.print.min.js"></script>
        <script src="datatables/buttons.colVis.min.js"></script>


        <script>
        (function($) {
            $(document).ready(function() {
                var table = $('#example').DataTable({
                    lengthChange: false,
                    buttons: [{
                            extend: 'excel',

                        },
                        {
                            extend: 'pdfHtml5',
                            orientation: 'landscape',
                            pageSize: 'A0'
                        },
                        {
                            extend: 'colvis',

                        }
                    ],
                });

                table.buttons().container()
                    .appendTo('#example_wrapper .col-sm-6:eq(0)');
            });
        })(jQuery);
        </script>