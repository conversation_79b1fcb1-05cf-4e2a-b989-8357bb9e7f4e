<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="open_sans_condensedbold" horiz-adv-x="948" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="506" />
<glyph unicode="&#xfb01;" horiz-adv-x="1108" d="M29 903v133l129 80v84q0 186 77 276.5t248 90.5q108 0 201 -45l-70 -203q-50 24 -102 24q-46 0 -67 -36t-21 -111v-82h184v-211h-184v-903h-266v903h-129zM716 1405q0 66 38.5 104.5t107.5 38.5q65 0 104 -38.5t39 -104.5q0 -68 -40 -105.5t-103 -37.5q-66 0 -106 37.5 t-40 105.5zM727 0v1114h266v-1114h-266z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1102" d="M29 903v133l129 80v84q0 186 77 276.5t248 90.5q108 0 201 -45l-70 -203q-50 24 -102 24q-46 0 -67 -36t-21 -111v-82h184v-211h-184v-903h-266v903h-129zM725 0v1556h266v-1556h-266z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1737" d="M29 903v133l129 80v84q0 186 77 276.5t248 90.5q108 0 201 -45l-70 -203q-50 24 -102 24q-46 0 -67 -36t-21 -111v-82h184v-211h-184v-903h-266v903h-129zM658 903v133l129 80v84q0 186 77 276.5t248 90.5q108 0 201 -45l-70 -203q-50 24 -102 24q-46 0 -67 -36t-21 -111 v-82h184v-211h-184v-903h-266v903h-129zM1345 1405q0 66 38.5 104.5t107.5 38.5q65 0 104 -38.5t39 -104.5q0 -68 -40 -105.5t-103 -37.5q-66 0 -106 37.5t-40 105.5zM1356 0v1114h266v-1114h-266z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1731" d="M29 903v133l129 80v84q0 186 77 276.5t248 90.5q108 0 201 -45l-70 -203q-50 24 -102 24q-46 0 -67 -36t-21 -111v-82h184v-211h-184v-903h-266v903h-129zM658 903v133l129 80v84q0 186 77 276.5t248 90.5q108 0 201 -45l-70 -203q-50 24 -102 24q-46 0 -67 -36t-21 -111 v-82h184v-211h-184v-903h-266v903h-129zM1354 0v1556h266v-1556h-266z" />
<glyph horiz-adv-x="2048" />
<glyph horiz-adv-x="2048" />
<glyph unicode="&#xd;" horiz-adv-x="1044" />
<glyph unicode=" "  horiz-adv-x="506" />
<glyph unicode="&#x09;" horiz-adv-x="506" />
<glyph unicode="&#xa0;" horiz-adv-x="506" />
<glyph unicode="!" horiz-adv-x="555" d="M111 137q0 78 41.5 121t123.5 43t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5zM119 1462h319l-51 -991h-219z" />
<glyph unicode="&#x22;" horiz-adv-x="913" d="M121 1462h262l-41 -528h-180zM530 1462h263l-41 -528h-181z" />
<glyph unicode="#" horiz-adv-x="1114" d="M41 410v194h217l37 252h-201v193h230l59 413h188l-59 -413h182l62 413h188l-59 -413h190v-193h-219l-37 -252h203v-194h-229l-60 -410h-192l61 410h-182l-58 -410h-192l59 410h-188zM449 604h180l37 252h-183z" />
<glyph unicode="$" d="M74 170v246q80 -47 169 -77t171 -34v338l-35 16q-170 81 -237.5 167.5t-67.5 217.5q0 150 89 241.5t251 109.5v161h116v-159q155 -10 308 -86l-80 -213q-128 62 -228 71v-317q31 -12 31 -14q127 -55 190 -106.5t93 -118t30 -156.5q0 -159 -89.5 -254.5t-254.5 -112.5 v-209h-116v205q-190 6 -340 84zM309 1044q0 -51 24 -82.5t81 -54.5v262q-105 -16 -105 -125zM530 311q109 19 109 140q0 45 -19.5 74.5t-89.5 64.5v-279z" />
<glyph unicode="%" horiz-adv-x="1554" d="M63 1022q0 461 289 461q295 0 295 -457q0 -226 -75.5 -343.5t-219.5 -117.5q-149 0 -219 115t-70 342zM276 1024q0 -139 18 -202.5t60 -63.5t61 63.5t19 202.5t-19.5 202.5t-60.5 63.5q-42 0 -60 -63.5t-18 -202.5zM371 0l602 1462h209l-604 -1462h-207zM907 438 q0 224 71.5 342.5t217.5 118.5q295 0 295 -457q0 -226 -76 -343t-219 -117q-150 0 -219.5 114.5t-69.5 341.5zM1120 440q0 -139 18 -202.5t60 -63.5t61 63.5t19 202.5t-19.5 202t-60.5 63t-59.5 -63t-18.5 -202z" />
<glyph unicode="&#x26;" horiz-adv-x="1190" d="M59 391q0 248 244 416q-77 119 -102 188t-25 146q0 156 97 249t255 93q152 0 242 -86.5t90 -239.5q0 -105 -55 -199.5t-184 -191.5l204 -287q34 62 53 138t33 151h232q-38 -280 -166 -498l194 -270h-299l-71 102q-136 -122 -338 -122q-192 0 -298 109t-106 302zM328 416 q0 -93 44 -146t120 -53q100 0 176 74l-240 346q-100 -90 -100 -221zM424 1130q0 -55 23.5 -101.5t58.5 -94.5q61 47 84.5 91t23.5 108q0 65 -26.5 94.5t-63.5 29.5q-46 0 -73 -32.5t-27 -94.5z" />
<glyph unicode="'" horiz-adv-x="504" d="M121 1462h262l-41 -528h-180z" />
<glyph unicode="(" horiz-adv-x="666" d="M74 561q0 265 77.5 496t223.5 405h235q-140 -193 -212 -424t-72 -475q0 -247 75 -477t207 -410h-233q-147 170 -224 397t-77 488z" />
<glyph unicode=")" horiz-adv-x="666" d="M55 1462h236q147 -175 224 -406.5t77 -494.5t-77.5 -490t-223.5 -395h-234q135 184 209 412.5t74 474.5q0 244 -72 475t-213 424z" />
<glyph unicode="*" horiz-adv-x="952" d="M53 1114l37 221l303 -86l-37 307h238l-37 -307l311 86l33 -225l-285 -18l185 -246l-199 -109l-129 260l-117 -260l-204 109l182 246z" />
<glyph unicode="+" horiz-adv-x="918" d="M106 618v207h254v373h197v-373h254v-207h-254v-368h-197v368h-254z" />
<glyph unicode="," horiz-adv-x="553" d="M51 -264q65 266 101 502h264l14 -23q-52 -202 -176 -479h-203z" />
<glyph unicode="-" horiz-adv-x="647" d="M72 432v234h503v-234h-503z" />
<glyph unicode="." horiz-adv-x="553" d="M111 137q0 78 41.5 121t123.5 43t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5z" />
<glyph unicode="/" horiz-adv-x="829" d="M16 0l545 1462h256l-545 -1462h-256z" />
<glyph unicode="0" d="M74 731q0 396 97 575t304 179q211 0 305 -175t94 -579q0 -397 -95.5 -574t-307.5 -177t-304.5 178t-92.5 573zM346 731q0 -307 26.5 -413.5t100.5 -106.5q50 0 77.5 46t39.5 152.5t12 321.5q0 302 -28 412t-101 110q-74 0 -100.5 -109.5t-26.5 -412.5z" />
<glyph unicode="1" d="M98 1102l389 360h242v-1462h-268v858q0 140 14 322q-7 -10 -51.5 -61t-173.5 -183z" />
<glyph unicode="2" d="M61 1276q105 119 195 163t195 44q177 0 276.5 -104t99.5 -289q0 -84 -18 -159t-54.5 -150.5t-91.5 -157t-284 -373.5v-8h479v-242h-784v203l260 368q107 152 147 225.5t59 141t19 139.5q0 84 -37.5 126t-97.5 42q-53 0 -99 -28t-112 -101z" />
<glyph unicode="3" d="M63 49v244q66 -40 143 -63t148 -23q116 0 175 53.5t59 169.5q0 219 -269 219h-83v203h79q119 0 182.5 56t63.5 167q0 87 -40.5 131.5t-114.5 44.5q-113 0 -220 -96l-123 180q148 148 373 148q185 0 289 -93.5t104 -256.5q0 -138 -68 -231.5t-192 -129.5v-8 q138 -22 213.5 -108t75.5 -234q0 -202 -125.5 -322t-343.5 -120q-194 0 -326 69z" />
<glyph unicode="4" d="M37 307v221l442 934h291v-930h148v-225h-148v-307h-256v307h-477zM266 532h248v308l5 136l7 130h-8q-35 -109 -76 -197z" />
<glyph unicode="5" d="M90 53v254q51 -37 131.5 -65.5t149.5 -28.5q215 0 215 250q0 233 -209 233q-83 0 -176 -32l-88 65l45 733h624v-243h-391l-20 -316q56 12 129 12q165 0 262.5 -115t97.5 -315q0 -239 -121.5 -372t-337.5 -133q-192 0 -311 73z" />
<glyph unicode="6" d="M74 621q0 447 139 653.5t424 206.5q45 0 90 -5.5t68 -11.5v-225q-69 16 -136 16q-114 0 -187 -50t-111 -149.5t-46 -279.5h11q35 83 96 125.5t141 42.5q147 0 229 -119.5t82 -341.5q0 -232 -108 -367.5t-293 -135.5q-196 0 -297.5 162t-101.5 479zM344 510 q0 -146 34.5 -223.5t96.5 -77.5q60 0 96.5 66t36.5 206q0 113 -32.5 178.5t-98.5 65.5q-59 0 -96 -61t-37 -154z" />
<glyph unicode="7" d="M80 1219v241h784v-184l-403 -1276h-281l410 1219h-510z" />
<glyph unicode="8" d="M72 371q0 126 53.5 222.5t165.5 168.5q-104 83 -148.5 167.5t-44.5 190.5q0 164 105.5 262.5t271.5 98.5q171 0 273 -97t102 -264q0 -103 -47.5 -186.5t-161.5 -159.5q128 -91 182 -186.5t54 -208.5q0 -179 -110 -289t-292 -110q-189 0 -296 101.5t-107 289.5zM330 385 q0 -88 36.5 -140t104.5 -52q74 0 110.5 52t36.5 140q0 63 -29 119t-112 129l-14 14q-65 -48 -99 -109.5t-34 -152.5zM358 1110q0 -66 24 -115t93 -104q66 51 90.5 102t24.5 117q0 160 -117 160q-57 0 -86 -41.5t-29 -118.5z" />
<glyph unicode="9" d="M68 973q0 234 107.5 371t287.5 137q196 0 300.5 -169t104.5 -487q0 -845 -565 -845q-94 0 -156 16v229q68 -22 127 -22q167 0 253.5 116t95.5 365h-9q-32 -79 -91.5 -123.5t-141.5 -44.5q-149 0 -231 122t-82 335zM334 979q0 -115 31 -179.5t94 -64.5q57 0 99 65.5 t42 149.5q0 130 -37 214.5t-98 84.5q-60 0 -95.5 -65t-35.5 -205z" />
<glyph unicode=":" horiz-adv-x="553" d="M111 137q0 78 41.5 121t123.5 43t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5zM111 973q0 78 41.5 121t123.5 43t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5z" />
<glyph unicode=";" horiz-adv-x="553" d="M51 -264q65 266 101 502h264l14 -23q-52 -202 -176 -479h-203zM111 973q0 78 41.5 121t123.5 43t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="918" d="M106 645v156l705 434v-225l-487 -287l487 -289v-225z" />
<glyph unicode="=" horiz-adv-x="918" d="M106 422v207h705v-207h-705zM106 815v205h705v-205h-705z" />
<glyph unicode="&#x3e;" horiz-adv-x="918" d="M106 209v225l490 289l-490 287v225l705 -434v-156z" />
<glyph unicode="?" horiz-adv-x="782" d="M25 1376q157 107 348 107q165 0 260.5 -91.5t95.5 -254.5q0 -114 -33 -193.5t-137 -191.5q-67 -73 -85.5 -112.5t-18.5 -107.5v-61h-236v70q0 96 29.5 167.5t107.5 153.5q74 81 94.5 127t20.5 117q0 65 -30 106t-95 41q-108 0 -227 -77zM172 137q0 79 42.5 121.5 t123.5 42.5q82 0 124 -43.5t42 -120.5q0 -76 -43 -120t-123 -44t-123 43t-43 121z" />
<glyph unicode="@" horiz-adv-x="1485" d="M74 594q0 260 89.5 462t253.5 311.5t374 109.5q294 0 457 -192t163 -537q0 -244 -82.5 -380.5t-230.5 -136.5q-69 0 -121 38.5t-68 101.5h-8q-34 -63 -87 -101.5t-122 -38.5q-123 0 -192.5 92.5t-69.5 280.5q0 223 98.5 356.5t264.5 133.5q118 0 256 -43l-21 -466v-38 q0 -82 17.5 -113t54.5 -31q50 0 79 93.5t29 253.5q0 274 -110 411t-312 137q-149 0 -265 -88t-179 -247.5t-63 -358.5q0 -279 122.5 -435.5t337.5 -156.5q178 0 365 90v-182q-165 -88 -367 -88t-352.5 91.5t-230.5 264.5t-80 406zM637 604q0 -96 22.5 -148.5t65.5 -52.5 q50 0 74 68.5t32 210.5l13 229q-24 11 -62 11q-65 0 -105 -90t-40 -228z" />
<glyph unicode="A" horiz-adv-x="1094" d="M0 0l362 1468h369l363 -1468h-273l-84 369h-381l-84 -369h-272zM410 610h276l-78 340q-7 31 -31.5 155.5t-29.5 166.5q-13 -86 -33 -189.5t-104 -472.5z" />
<glyph unicode="B" horiz-adv-x="1038" d="M129 0v1462h373q228 0 332 -88.5t104 -281.5q0 -127 -55 -212.5t-150 -103.5v-10q124 -31 179 -116t55 -226q0 -199 -106.5 -311.5t-291.5 -112.5h-440zM385 238h137q85 0 126.5 52t41.5 161q0 196 -172 196h-133v-409zM385 874h125q86 0 121 46t35 137t-40 130.5 t-126 39.5h-115v-353z" />
<glyph unicode="C" d="M84 729q0 356 136.5 555t379.5 199q80 0 153.5 -22t157.5 -77l-90 -221q-45 31 -96 56.5t-111 25.5q-117 0 -184.5 -137t-67.5 -381q0 -508 265 -508q116 0 243 74v-242q-107 -71 -274 -71q-247 0 -379.5 196.5t-132.5 552.5z" />
<glyph unicode="D" horiz-adv-x="1143" d="M129 0v1462h391q255 0 397 -188.5t142 -528.5q0 -362 -147.5 -553.5t-424.5 -191.5h-358zM401 238h88q149 0 220 124.5t71 374.5q0 490 -270 490h-109v-989z" />
<glyph unicode="E" horiz-adv-x="872" d="M129 0v1462h659v-235h-387v-348h359v-236h-359v-405h387v-238h-659z" />
<glyph unicode="F" horiz-adv-x="838" d="M129 0v1462h655v-235h-385v-406h359v-235h-359v-586h-270z" />
<glyph unicode="G" horiz-adv-x="1112" d="M84 733q0 352 157.5 551t440.5 199q175 0 334 -90l-86 -222q-124 72 -232 72q-155 0 -245.5 -138.5t-90.5 -377.5q0 -246 65.5 -377t190.5 -131q66 0 134 17v335h-181v240h447v-750q-190 -81 -404 -81q-255 0 -392.5 194t-137.5 559z" />
<glyph unicode="H" horiz-adv-x="1161" d="M129 0v1462h272v-581h359v581h272v-1462h-272v641h-359v-641h-272z" />
<glyph unicode="I" horiz-adv-x="614" d="M178 0v1462h256v-1462h-256z" />
<glyph unicode="J" horiz-adv-x="530" d="M-154 -160q65 -20 123 -20q83 0 121.5 53t38.5 158v1431h272v-1417q0 -144 -48.5 -250t-137 -161.5t-201.5 -55.5q-106 0 -168 25v237z" />
<glyph unicode="K" horiz-adv-x="1040" d="M129 0v1462h272v-725l95 219l249 506h291l-338 -633l342 -829h-290l-240 625l-109 -109v-516h-272z" />
<glyph unicode="L" horiz-adv-x="823" d="M129 0v1462h272v-1224h377v-238h-649z" />
<glyph unicode="M" horiz-adv-x="1606" d="M129 0v1462h418l250 -1149h8l248 1149h424v-1462h-258v991q0 123 14 252h-8l-277 -1243h-297l-278 1245h-11q19 -144 19 -264v-981h-252z" />
<glyph unicode="N" horiz-adv-x="1282" d="M129 0v1462h334l448 -1048h11q-19 105 -19 250v798h250v-1462h-332l-452 1059h-13q25 -121 25 -260v-799h-252z" />
<glyph unicode="O" horiz-adv-x="1219" d="M84 735q0 363 136 556.5t392 193.5q253 0 388 -194.5t135 -557.5t-136 -558t-389 -195q-254 0 -390 194.5t-136 560.5zM362 733q0 -250 63 -381t185 -131q246 0 246 512t-244 512q-124 0 -187 -130.5t-63 -381.5z" />
<glyph unicode="P" horiz-adv-x="981" d="M129 0v1462h371q426 0 426 -450q0 -233 -117.5 -357.5t-339.5 -124.5h-68v-530h-272zM401 766h52q100 0 149 55t49 180q0 115 -45 170.5t-139 55.5h-66v-461z" />
<glyph unicode="Q" horiz-adv-x="1219" d="M84 735q0 363 136 556.5t392 193.5q253 0 388 -194.5t135 -557.5q0 -251 -66.5 -424t-196.5 -258l269 -399h-314l-202 328h-15q-254 0 -390 194.5t-136 560.5zM362 733q0 -250 63 -381t185 -131q246 0 246 512t-244 512q-124 0 -187 -130.5t-63 -381.5z" />
<glyph unicode="R" horiz-adv-x="1024" d="M129 0v1462h356q226 0 333.5 -107t107.5 -327q0 -277 -187 -387l281 -641h-291l-231 569h-97v-569h-272zM401 803h70q96 0 138 54.5t42 162.5q0 112 -44.5 159.5t-137.5 47.5h-68v-424z" />
<glyph unicode="S" horiz-adv-x="846" d="M68 55v269q164 -107 301 -107q78 0 117.5 43t39.5 117q0 73 -47 133.5t-155 134.5q-94 65 -142.5 124.5t-75 133.5t-26.5 172q0 188 104.5 298t280.5 110q168 0 323 -94l-90 -218q-48 28 -100.5 50t-112.5 22q-66 0 -101.5 -43.5t-35.5 -118.5q0 -74 42.5 -129 t148.5 -125q143 -96 199.5 -196t56.5 -228q0 -205 -103.5 -314t-298.5 -109q-191 0 -325 75z" />
<glyph unicode="T" horiz-adv-x="885" d="M27 1223v239h831v-239h-280v-1223h-271v1223h-280z" />
<glyph unicode="U" horiz-adv-x="1151" d="M123 520v942h270v-962q0 -279 185 -279q86 0 133 71t47 202v968h270v-946q0 -261 -118 -398.5t-341 -137.5q-218 0 -332 136.5t-114 403.5z" />
<glyph unicode="V" horiz-adv-x="1032" d="M0 1462h274l191 -893q26 -114 51 -327q21 181 53 327l189 893h274l-344 -1462h-346z" />
<glyph unicode="W" horiz-adv-x="1622" d="M18 1462h267l131 -825q19 -124 51 -389q25 268 47 383l156 831h282l156 -831q31 -185 49 -385q35 296 51 391l127 825h269l-263 -1462h-340l-143 766q-33 157 -47 371l-8 -74l-41 -301l-139 -762h-340z" />
<glyph unicode="X" horiz-adv-x="969" d="M0 0l334 760l-309 702h274l193 -481l167 481h285l-309 -714l334 -748h-283l-209 524l-194 -524h-283z" />
<glyph unicode="Y" horiz-adv-x="961" d="M0 1462h287l194 -577l195 577h285l-347 -876v-586h-268v575z" />
<glyph unicode="Z" horiz-adv-x="877" d="M43 0v190l467 1035h-453v237h762v-190l-467 -1034h482v-238h-791z" />
<glyph unicode="[" horiz-adv-x="649" d="M133 -324v1786h469v-198h-223v-1389h223v-199h-469z" />
<glyph unicode="\" horiz-adv-x="829" d="M14 1462h258l545 -1462h-258z" />
<glyph unicode="]" horiz-adv-x="649" d="M47 -125h223v1389h-223v198h469v-1786h-469v199z" />
<glyph unicode="^" horiz-adv-x="1090" d="M14 526l437 944h135l493 -944h-225l-330 660l-288 -660h-222z" />
<glyph unicode="_" horiz-adv-x="696" d="M-4 -184h704v-140h-704v140z" />
<glyph unicode="`" horiz-adv-x="1114" d="M311 1548v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5z" />
<glyph unicode="a" horiz-adv-x="956" d="M55 328q0 176 96.5 261.5t288.5 94.5l148 6v80q0 156 -135 156q-100 0 -230 -78l-92 184q166 105 361 105q177 0 269.5 -101.5t92.5 -287.5v-748h-195l-53 152h-6q-59 -98 -122 -135t-159 -37q-123 0 -193.5 92t-70.5 256zM330 326q0 -138 98 -138q73 0 116.5 65.5 t43.5 174.5v94l-82 -4q-91 -5 -133.5 -52t-42.5 -140z" />
<glyph unicode="b" horiz-adv-x="1016" d="M113 0v1556h266v-364q0 -23 -1 -48t-13 -175h14q49 92 106 129t131 37q152 0 239 -151t87 -425q0 -277 -87.5 -428t-242.5 -151q-75 0 -132.5 32.5t-108.5 114.5h-17l-45 -127h-196zM379 561q0 -194 34.5 -273t112.5 -79q144 0 144 354q0 346 -146 346q-76 0 -109.5 -71 t-35.5 -250v-27z" />
<glyph unicode="c" horiz-adv-x="760" d="M74 551q0 294 98.5 439t304.5 145q62 0 130 -18t122 -52l-76 -207q-83 49 -147 49q-85 0 -122.5 -88.5t-37.5 -265.5q0 -173 37 -258.5t121 -85.5q100 0 209 70v-234q-105 -65 -232 -65q-199 0 -303 145t-104 426z" />
<glyph unicode="d" horiz-adv-x="1016" d="M74 555q0 272 87 426t240 154q76 0 131.5 -37.5t102.5 -126.5h16q-12 152 -12 213v372h266v-1556h-198l-45 145h-9q-85 -165 -247 -165q-154 0 -243 152t-89 423zM348 551q0 -173 34 -259.5t110 -86.5q74 0 108 67.5t37 245.5v35q0 194 -34.5 273t-113.5 79 q-74 0 -107.5 -90.5t-33.5 -263.5z" />
<glyph unicode="e" horiz-adv-x="942" d="M74 549q0 276 109 431t304 155q182 0 287 -132.5t105 -371.5v-142h-535q3 -149 61 -221.5t160 -72.5q130 0 266 81v-219q-128 -77 -307 -77q-209 0 -329.5 148.5t-120.5 420.5zM348 682h270q-2 122 -37 185t-96 63q-57 0 -94 -60.5t-43 -187.5z" />
<glyph unicode="f" horiz-adv-x="612" d="M29 903v133l129 80v84q0 186 77 276.5t248 90.5q108 0 201 -45l-70 -203q-50 24 -102 24q-46 0 -67 -36t-21 -111v-82h184v-211h-184v-903h-266v903h-129z" />
<glyph unicode="g" horiz-adv-x="936" d="M18 -182q0 93 57 163.5t158 102.5q-51 20 -83.5 62t-32.5 98q0 34 10 61.5t28.5 52t75.5 72.5q-69 42 -109 129t-40 193q0 183 96 283t275 100q34 0 78 -7.5t67 -15.5h317v-156l-133 -30q39 -70 39 -176q0 -177 -96 -277t-268 -100q-50 0 -74 8q-19 -17 -36 -38t-17 -52 q0 -70 121 -70h161q142 0 218.5 -76.5t76.5 -230.5q0 -194 -130 -300t-374 -106q-185 0 -285 80.5t-100 229.5zM260 -162q0 -66 41.5 -101.5t118.5 -35.5q121 0 181.5 46t60.5 126q0 63 -32.5 89t-121.5 26h-129q-54 0 -86.5 -40.5t-32.5 -109.5zM342 750q0 -197 111 -197 q108 0 108 199q0 204 -108 204q-111 0 -111 -206z" />
<glyph unicode="h" horiz-adv-x="1038" d="M113 0v1556h266v-346q0 -67 -12 -239h14q80 164 246 164q153 0 231 -103.5t78 -304.5v-727h-268v664q0 245 -123 245q-92 0 -129 -96.5t-37 -277.5v-535h-266z" />
<glyph unicode="i" horiz-adv-x="496" d="M104 1405q0 66 38.5 104.5t107.5 38.5q65 0 104 -38.5t39 -104.5q0 -68 -40 -105.5t-103 -37.5q-66 0 -106 37.5t-40 105.5zM115 0v1114h266v-1114h-266z" />
<glyph unicode="j" horiz-adv-x="500" d="M-59 -246q46 -18 86 -18q94 0 94 170v1208h266v-1243q0 -174 -80.5 -268.5t-226.5 -94.5q-84 0 -139 25v221zM111 1405q0 66 38.5 104.5t106.5 38.5q67 0 105 -39t38 -104q0 -67 -39 -105t-104 -38t-105 37t-40 106z" />
<glyph unicode="k" horiz-adv-x="965" d="M113 0v1556h266v-733l-12 -223h8l67 133l220 381h282l-282 -457l301 -657h-279l-201 471l-104 -94v-377h-266z" />
<glyph unicode="l" horiz-adv-x="489" d="M113 0v1556h266v-1556h-266z" />
<glyph unicode="m" horiz-adv-x="1589" d="M113 0v1114h211l32 -143h17q40 85 109.5 124.5t148.5 39.5q204 0 272 -193h10q85 193 273 193q148 0 224.5 -104t76.5 -304v-727h-268v664q0 245 -121 245q-88 0 -126 -87.5t-38 -280.5v-541h-268v664q0 245 -123 245q-86 0 -125 -87t-39 -287v-535h-266z" />
<glyph unicode="n" horiz-adv-x="1038" d="M113 0v1114h211l32 -143h17q36 77 103 120.5t157 43.5q152 0 227.5 -104t75.5 -304v-727h-268v664q0 121 -29 183t-94 62q-86 0 -126 -85.5t-40 -288.5v-535h-266z" />
<glyph unicode="o" horiz-adv-x="1012" d="M74 559q0 271 115 423.5t319 152.5q197 0 313.5 -155.5t116.5 -420.5q0 -274 -116 -426.5t-318 -152.5q-128 0 -226 70t-151 201.5t-53 307.5zM346 559q0 -174 38 -263t122 -89q160 0 160 352q0 175 -38.5 261.5t-121.5 86.5q-84 0 -122 -86.5t-38 -261.5z" />
<glyph unicode="p" horiz-adv-x="1016" d="M113 -492v1606h206l46 -145h12q83 166 241 166q152 0 238 -150.5t86 -425.5q0 -271 -89 -425t-243 -154q-72 0 -127.5 32.5t-103.5 114.5h-14q16 -136 16 -164v-455h-268zM379 561q0 -184 35 -268t116 -84q140 0 140 354q0 174 -34.5 260t-107.5 86q-78 0 -112.5 -72.5 t-36.5 -240.5v-35z" />
<glyph unicode="q" horiz-adv-x="1016" d="M74 555q0 273 86.5 426.5t238.5 153.5q78 0 135 -38t103 -126h14l29 143h225v-1606h-266v469q0 36 12 168h-10q-42 -86 -100 -125.5t-135 -39.5q-156 0 -244 152t-88 423zM348 551q0 -177 33.5 -262.5t105.5 -85.5q79 0 113.5 72.5t36.5 240.5v37q0 184 -35 268t-115 84 q-139 0 -139 -354z" />
<glyph unicode="r" horiz-adv-x="690" d="M113 0v1114h219l30 -174h9q44 105 96 150t121 45q49 0 96 -15l-31 -258q-28 10 -73 10q-97 0 -149 -76.5t-52 -222.5v-573h-266z" />
<glyph unicode="s" horiz-adv-x="766" d="M61 45v231q55 -38 128.5 -63t129.5 -25q62 0 94.5 30.5t32.5 80.5q0 51 -34 89t-125 93q-121 72 -173.5 153.5t-52.5 192.5q0 142 97.5 225t267.5 83q148 0 279 -78l-87 -191q-107 58 -188 58q-51 0 -76.5 -27t-25.5 -66q0 -45 31.5 -80t117.5 -87q104 -63 146 -107 t65 -100t23 -131q0 -163 -95.5 -254.5t-281.5 -91.5q-165 0 -273 65z" />
<glyph unicode="t" horiz-adv-x="664" d="M33 903v119l139 96l68 236h170v-240h202v-211h-202v-586q0 -112 90 -112q56 0 121 28v-208q-65 -45 -195 -45q-145 0 -215 86.5t-70 259.5v577h-108z" />
<glyph unicode="u" horiz-adv-x="1038" d="M102 387v727h269v-663q0 -122 29 -184t94 -62q86 0 125.5 86t39.5 289v534h269v-1114h-213l-33 143h-14q-37 -76 -105.5 -119.5t-156.5 -43.5q-150 0 -227 102t-77 305z" />
<glyph unicode="v" horiz-adv-x="913" d="M0 1114h276l142 -645q9 -45 21.5 -131.5t15.5 -124.5h4q1 30 8.5 86t15.5 103t154 712h276l-292 -1114h-330z" />
<glyph unicode="w" horiz-adv-x="1479" d="M20 1114h267l102 -614q18 -102 33 -299h6q2 53 14 161.5t17 139.5l110 612h342l109 -614q19 -98 33 -299h4q14 179 35 299l106 614h260l-223 -1114h-346l-111 647l-39 273h-4q-15 -133 -24 -192.5t-127 -727.5h-342z" />
<glyph unicode="x" horiz-adv-x="889" d="M14 0l291 569l-278 545h276l148 -340l137 340h274l-280 -545l292 -569h-278l-158 356l-147 -356h-277z" />
<glyph unicode="y" horiz-adv-x="913" d="M0 1114h276l154 -629q23 -84 37 -235h6q1 24 9 85.5t13 96.5t138 682h280l-319 -1194q-58 -220 -154 -316t-256 -96q-79 0 -141 17v223q41 -12 92 -12q134 0 176 176l19 70z" />
<glyph unicode="z" horiz-adv-x="731" d="M41 0v170l342 729h-320v215h613v-188l-332 -711h346v-215h-649z" />
<glyph unicode="{" horiz-adv-x="737" d="M25 457v223q124 0 191 40t67 132v8v320q0 108 41.5 167.5t133 87t247.5 27.5v-211q-101 -3 -138 -37.5t-37 -107.5v-303q-3 -194 -231 -228v-12q231 -34 231 -215v-12v-303q0 -73 36.5 -108t138.5 -38v-211q-158 0 -248.5 27.5t-132 87.5t-41.5 168v317q0 99 -67 140 t-191 41z" />
<glyph unicode="|" horiz-adv-x="1038" d="M416 -471v2023h207v-2023h-207z" />
<glyph unicode="}" horiz-adv-x="737" d="M33 -113q102 3 139 37.5t37 108.5v303v9q0 91 59.5 146t173.5 72v12q-229 34 -233 228v303q0 74 -37 108t-139 37v211q234 0 329 -63.5t95 -218.5v-320v-8q0 -92 65.5 -132t190.5 -40v-223q-125 0 -190.5 -41t-65.5 -140v-317q0 -156 -95.5 -219.5t-328.5 -63.5v211z" />
<glyph unicode="~" horiz-adv-x="918" d="M74 557v219q90 109 207 109q43 0 80.5 -10.5t123.5 -53.5q67 -34 99 -44.5t73 -10.5q87 0 187 121v-219q-81 -109 -207 -109q-51 0 -99.5 16.5t-107.5 47.5q-82 41 -108 48t-58 7q-88 0 -190 -121z" />
<glyph unicode="&#xa1;" horiz-adv-x="555" d="M111 954q0 78 43.5 121t119.5 43q82 0 125 -43.5t43 -120.5q0 -79 -43 -121t-125 -42q-78 0 -120.5 42t-42.5 121zM113 -369l51 990h221l51 -990h-323z" />
<glyph unicode="&#xa2;" d="M147 737q0 252 73.5 395t225.5 179v168h166v-160q106 -16 193 -66l-78 -209q-85 50 -147 50q-85 0 -122.5 -87.5t-37.5 -267.5q0 -171 37 -257.5t121 -86.5q100 0 208 72v-234q-77 -50 -174 -61v-192h-166v200q-299 79 -299 557z" />
<glyph unicode="&#xa3;" d="M76 0v229q92 49 124.5 108.5t32.5 156.5v157h-155v203h155v219q0 199 93.5 304.5t271.5 105.5q157 0 283 -72l-84 -211q-92 53 -183 53q-114 0 -114 -186v-213h241v-203h-241v-133q0 -91 -30.5 -158.5t-100.5 -117.5h534v-242h-827z" />
<glyph unicode="&#xa4;" horiz-adv-x="1112" d="M90 1051l137 137l127 -127q93 55 201 55q107 0 199 -57l129 129l139 -133l-129 -129q55 -93 55 -203q0 -114 -55 -203l125 -125l-135 -135l-129 125q-93 -53 -199 -53q-120 0 -203 53l-125 -123l-135 135l127 125q-57 92 -57 201q0 102 57 201zM356 723q0 -83 58 -141 t141 -58q85 0 144 58.5t59 140.5q0 84 -59 142.5t-144 58.5q-83 0 -141 -58t-58 -143z" />
<glyph unicode="&#xa5;" d="M6 1462h283l186 -618l184 618h283l-276 -751h186v-170h-244v-142h244v-170h-244v-229h-266v229h-246v170h246v142h-246v170h187z" />
<glyph unicode="&#xa6;" horiz-adv-x="1038" d="M416 338h207v-809h-207v809zM416 743v809h207v-809h-207z" />
<glyph unicode="&#xa7;" horiz-adv-x="872" d="M82 23v192q152 -72 295 -72q81 0 120.5 28t39.5 91q0 34 -31.5 66.5t-102.5 73.5q-143 80 -198 130.5t-83 110t-28 133.5q0 79 34.5 142.5t94.5 103.5q-59 40 -91 106t-32 142q0 133 92.5 211t253.5 78q97 0 175.5 -20.5t150.5 -49.5l-65 -184q-63 27 -127 49t-136 22 q-58 0 -86 -28.5t-28 -73.5q0 -37 31 -71t104 -75l84 -49q125 -71 177 -149t52 -178q0 -167 -125 -260q56 -41 85.5 -90t29.5 -121q0 -148 -94 -232t-271 -84q-113 0 -187 14.5t-134 43.5zM307 797q0 -56 32 -94t114 -87l61 -36q43 62 43 137q0 63 -49 118.5t-135 98.5 q-26 -16 -46 -52t-20 -85z" />
<glyph unicode="&#xa8;" horiz-adv-x="1130" d="M233 1403q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM631 1403q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1688" d="M92 731q0 200 100 375t275 276t377 101q197 0 370 -97t277 -272t104 -383q0 -204 -100.5 -376.5t-273 -273.5t-377.5 -101q-207 0 -382 103.5t-272.5 276.5t-97.5 371zM227 731q0 -165 83 -308.5t225.5 -225.5t308.5 -82t309 82t225 225t82 309t-82 308.5t-225 225.5 t-309 83q-165 0 -308.5 -82.5t-226 -225.5t-82.5 -309zM457 733q0 219 110 341t307 122q142 0 295 -74l-71 -161q-111 57 -211 57q-102 0 -157.5 -75.5t-55.5 -211.5q0 -140 49.5 -214.5t163.5 -74.5q56 0 124 16.5t115 41.5v-181q-116 -55 -245 -55q-201 0 -312.5 123.5 t-111.5 345.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="682" d="M57 977q0 108 66.5 161.5t212.5 61.5l82 4v8q0 119 -103 119q-69 0 -163 -57l-56 127q128 78 256 78q116 0 175 -68.5t59 -193.5v-447h-117l-27 104q-32 -55 -81 -85.5t-109 -30.5q-89 0 -142 53t-53 166zM229 979q0 -78 64 -78q57 0 91 40t34 112v39l-72 -7 q-58 -5 -87.5 -30t-29.5 -76z" />
<glyph unicode="&#xab;" horiz-adv-x="1141" d="M74 543v26l307 447l199 -111l-220 -348l220 -348l-199 -111zM561 543v26l307 447l199 -111l-219 -348l219 -348l-199 -111z" />
<glyph unicode="&#xac;" horiz-adv-x="918" d="M106 618v207h705v-575h-195v368h-510z" />
<glyph unicode="&#xad;" horiz-adv-x="647" d="M72 432v234h503v-234h-503z" />
<glyph unicode="&#xae;" horiz-adv-x="1688" d="M92 731q0 200 100 375t275 276t377 101q197 0 370 -97t277 -272t104 -383q0 -204 -100.5 -376.5t-273 -273.5t-377.5 -101q-207 0 -382 103.5t-272.5 276.5t-97.5 371zM227 731q0 -165 83 -308.5t225.5 -225.5t308.5 -82t309 82t225 225t82 309t-82 308.5t-225 225.5 t-309 83q-165 0 -308.5 -82.5t-226 -225.5t-82.5 -309zM543 274v912h258q180 0 263 -68t83 -211q0 -170 -148 -233l19 -29l219 -371h-238l-184 342h-57v-342h-215zM758 776h39q71 0 102 29.5t31 97.5q0 70 -33 95.5t-102 25.5h-37v-248z" />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M-6 1556v189h1036v-189h-1036z" />
<glyph unicode="&#xb0;" horiz-adv-x="831" d="M76 1143q0 139 99 239.5t241 100.5q139 0 239.5 -100t100.5 -240q0 -141 -99 -239.5t-241 -98.5q-143 0 -241.5 98.5t-98.5 239.5zM252 1143q0 -67 49 -114.5t115 -47.5t114 47.5t48 114.5q0 68 -47.5 116t-114.5 48t-115.5 -49t-48.5 -115z" />
<glyph unicode="&#xb1;" horiz-adv-x="918" d="M106 0v207h705v-207h-705zM106 618v207h254v373h197v-373h254v-207h-254v-368h-197v368h-254z" />
<glyph unicode="&#xb2;" horiz-adv-x="688" d="M49 1356q127 127 289 127q122 0 191 -67t69 -183q0 -81 -38.5 -154t-160.5 -200l-102 -107h326v-186h-570v157l199 222q95 107 122 153.5t27 98.5q0 42 -23.5 65t-62.5 23q-75 0 -163 -88z" />
<glyph unicode="&#xb3;" horiz-adv-x="688" d="M57 1374q71 59 137 83t140 24q111 0 182.5 -62.5t71.5 -167.5q0 -149 -148 -200v-11q89 -21 131.5 -78.5t42.5 -130.5q0 -121 -78 -191.5t-229 -70.5q-156 0 -244 56v176q106 -72 230 -72q135 0 135 117q0 112 -154 112h-98v150h84q73 0 110.5 27.5t37.5 89.5 q0 43 -24 68.5t-71 25.5q-44 0 -82.5 -19t-87.5 -59z" />
<glyph unicode="&#xb4;" horiz-adv-x="1114" d="M311 1241v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xb5;" horiz-adv-x="1038" d="M102 -492v1606h269v-700q0 -114 30 -161.5t93 -47.5q86 0 125.5 86t39.5 289v534h269v-1114h-213l-33 143h-16q-63 -163 -199 -163q-59 0 -105 40q9 -130 9 -272v-240h-269z" />
<glyph unicode="&#xb6;" horiz-adv-x="1083" d="M63 1042q0 257 80.5 385.5t255.5 128.5h549v-1816h-153v1624h-162v-1624h-154v819q-45 -18 -106 -18q-160 0 -235 125t-75 376z" />
<glyph unicode="&#xb7;" horiz-adv-x="553" d="M111 723q0 78 41.5 121t123.5 43t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="416" d="M-25 -317q61 -17 121 -17q88 0 88 66q0 84 -157 110l79 158h173l-37 -76q68 -17 111.5 -63t43.5 -121q0 -107 -71 -169.5t-220 -62.5q-73 0 -131 21v154z" />
<glyph unicode="&#xb9;" horiz-adv-x="688" d="M66 1235l272 227h162v-876h-197v461l3 112l5 96q-7 -10 -22 -24t-131 -113z" />
<glyph unicode="&#xba;" horiz-adv-x="686" d="M61 1120q0 170 75 264.5t208 94.5q128 0 204.5 -96.5t76.5 -262.5q0 -171 -76 -266.5t-207 -95.5q-130 0 -205.5 98.5t-75.5 263.5zM233 1120q0 -106 27 -157.5t84 -51.5t83 51.5t26 157.5t-26 155.5t-83 49.5t-84 -49.5t-27 -155.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1141" d="M74 209l219 348l-219 348l198 111l308 -447v-26l-308 -445zM561 209l219 348l-219 348l199 111l307 -447v-26l-307 -445z" />
<glyph unicode="&#xbc;" horiz-adv-x="1597" d="M59 1235l272 227h162v-876h-197v461l3 112l5 96q-7 -10 -22 -24t-131 -113zM276 0l742 1462h204l-743 -1462h-203zM829 160v146l347 577h194v-563h111v-160h-111v-159h-196v159h-345zM987 320h187v170l4 187q-12 -33 -34.5 -84t-35.5 -72z" />
<glyph unicode="&#xbd;" horiz-adv-x="1597" d="M59 1235l272 227h162v-876h-197v461l3 112l5 96q-7 -10 -22 -24t-131 -113zM276 0l742 1462h204l-743 -1462h-203zM944 771q127 127 289 127q122 0 191 -67t69 -183q0 -81 -38.5 -154t-160.5 -200l-102 -107h326v-186h-570v157l199 222q95 107 122 153.5t27 98.5 q0 42 -23.5 65t-62.5 23q-75 0 -163 -88z" />
<glyph unicode="&#xbe;" horiz-adv-x="1597" d="M61 1374q71 59 137 83t140 24q111 0 182.5 -62.5t71.5 -167.5q0 -149 -148 -200v-11q89 -21 131.5 -78.5t42.5 -130.5q0 -121 -78 -191.5t-229 -70.5q-156 0 -244 56v176q106 -72 230 -72q135 0 135 117q0 112 -154 112h-98v150h84q73 0 110.5 27.5t37.5 89.5 q0 43 -24 68.5t-71 25.5q-44 0 -82.5 -19t-87.5 -59zM311 0l742 1462h204l-743 -1462h-203zM835 160v146l347 577h194v-563h111v-160h-111v-159h-196v159h-345zM993 320h187v170l4 187q-12 -33 -34.5 -84t-35.5 -72z" />
<glyph unicode="&#xbf;" horiz-adv-x="782" d="M53 -45q0 76 14 134t44 109.5t112 141.5q66 70 85.5 111.5t19.5 107.5v62h235v-72q0 -94 -29.5 -165.5t-107.5 -156.5q-74 -81 -94.5 -127t-20.5 -116q0 -63 31 -104.5t94 -41.5q109 0 226 78l96 -201q-158 -106 -348 -106q-167 0 -262 90.5t-95 255.5zM279 954 q0 78 43.5 121t119.5 43q82 0 125 -43.5t43 -120.5q0 -79 -43 -121t-125 -42q-78 0 -120.5 42t-42.5 121z" />
<glyph unicode="&#xc0;" horiz-adv-x="1094" d="M0 0l362 1468h369l363 -1468h-273l-84 369h-381l-84 -369h-272zM193 1886v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5zM410 610h276l-78 340q-7 31 -31.5 155.5t-29.5 166.5q-13 -86 -33 -189.5t-104 -472.5z" />
<glyph unicode="&#xc1;" horiz-adv-x="1094" d="M0 0l362 1468h369l363 -1468h-273l-84 369h-381l-84 -369h-272zM405 1579v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187zM410 610h276l-78 340q-7 31 -31.5 155.5t-29.5 166.5q-13 -86 -33 -189.5t-104 -472.5z" />
<glyph unicode="&#xc2;" horiz-adv-x="1094" d="M0 0l362 1468h369l363 -1468h-273l-84 369h-381l-84 -369h-272zM192 1579v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168zM410 610h276l-78 340q-7 31 -31.5 155.5t-29.5 166.5q-13 -86 -33 -189.5t-104 -472.5z" />
<glyph unicode="&#xc3;" horiz-adv-x="1094" d="M0 0l362 1468h369l363 -1468h-273l-84 369h-381l-84 -369h-272zM210 1577q11 153 68.5 227t150.5 74q43 0 77.5 -16.5t64.5 -36.5t58 -36.5t58 -16.5q34 0 49.5 26t26.5 82h141q-11 -152 -69.5 -226.5t-149.5 -74.5q-44 0 -78.5 16.5t-64 37t-56.5 37t-57 16.5 q-31 0 -49.5 -23.5t-28.5 -85.5h-141zM410 610h276l-78 340q-7 31 -31.5 155.5t-29.5 166.5q-13 -86 -33 -189.5t-104 -472.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="1094" d="M0 0l362 1468h369l363 -1468h-273l-84 369h-381l-84 -369h-272zM216 1741q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM410 610h276l-78 340q-7 31 -31.5 155.5t-29.5 166.5q-13 -86 -33 -189.5t-104 -472.5zM614 1741 q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1094" d="M0 0l362 1468h369l363 -1468h-273l-84 369h-381l-84 -369h-272zM302 1569q0 108 67 170.5t177 62.5q109 0 179.5 -64t70.5 -167q0 -107 -69.5 -171.5t-180.5 -64.5q-110 0 -177 62.5t-67 171.5zM410 610h276l-78 340q-7 31 -31.5 155.5t-29.5 166.5q-13 -86 -33 -189.5 t-104 -472.5zM446 1569q0 -47 25 -74t75 -27q43 0 71.5 27t28.5 74q0 46 -28.5 73t-71.5 27t-71.5 -27t-28.5 -73z" />
<glyph unicode="&#xc6;" horiz-adv-x="1425" d="M0 0l473 1462h866v-235h-383v-348h355v-236h-355v-405h383v-238h-655v369h-299l-113 -369h-272zM461 610h223v617h-39l-14 -49z" />
<glyph unicode="&#xc7;" d="M84 729q0 356 136.5 555t379.5 199q80 0 153.5 -22t157.5 -77l-90 -221q-45 31 -96 56.5t-111 25.5q-117 0 -184.5 -137t-67.5 -381q0 -508 265 -508q116 0 243 74v-242q-107 -71 -274 -71q-247 0 -379.5 196.5t-132.5 552.5zM325 -317q61 -17 121 -17q88 0 88 66 q0 84 -157 110l79 158h173l-37 -76q68 -17 111.5 -63t43.5 -121q0 -107 -71 -169.5t-220 -62.5q-73 0 -131 21v154z" />
<glyph unicode="&#xc8;" horiz-adv-x="872" d="M89 1886v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5zM129 0v1462h659v-235h-387v-348h359v-236h-359v-405h387v-238h-659z" />
<glyph unicode="&#xc9;" horiz-adv-x="872" d="M129 0v1462h659v-235h-387v-348h359v-236h-359v-405h387v-238h-659zM336 1579v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xca;" horiz-adv-x="872" d="M103 1579v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168zM129 0v1462h659v-235h-387v-348h359v-236h-359v-405h387v-238h-659z" />
<glyph unicode="&#xcb;" horiz-adv-x="872" d="M129 0v1462h659v-235h-387v-348h359v-236h-359v-405h387v-238h-659zM130 1741q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM528 1741q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5 t-35.5 92.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="614" d="M-50 1886v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5zM178 0v1462h256v-1462h-256z" />
<glyph unicode="&#xcd;" horiz-adv-x="614" d="M167 1579v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187zM178 0v1462h256v-1462h-256z" />
<glyph unicode="&#xce;" horiz-adv-x="614" d="M-48 1579v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168zM178 0v1462h256v-1462h-256z" />
<glyph unicode="&#xcf;" horiz-adv-x="614" d="M-24 1741q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM178 0v1462h256v-1462h-256zM374 1741q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1143" d="M0 604v238h129v620h391q255 0 397 -188.5t142 -528.5q0 -362 -147.5 -553.5t-424.5 -191.5h-358v604h-129zM401 238h88q149 0 220 124.5t71 374.5q0 490 -270 490h-109v-385h183v-238h-183v-366z" />
<glyph unicode="&#xd1;" horiz-adv-x="1282" d="M129 0v1462h334l448 -1048h11q-19 105 -19 250v798h250v-1462h-332l-452 1059h-13q25 -121 25 -260v-799h-252zM303 1577q11 153 68.5 227t150.5 74q43 0 77.5 -16.5t64.5 -36.5t58 -36.5t58 -16.5q34 0 49.5 26t26.5 82h141q-11 -152 -69.5 -226.5t-149.5 -74.5 q-44 0 -78.5 16.5t-64 37t-56.5 37t-57 16.5q-31 0 -49.5 -23.5t-28.5 -85.5h-141z" />
<glyph unicode="&#xd2;" horiz-adv-x="1219" d="M84 735q0 363 136 556.5t392 193.5q253 0 388 -194.5t135 -557.5t-136 -558t-389 -195q-254 0 -390 194.5t-136 560.5zM244 1886v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5zM362 733q0 -250 63 -381t185 -131q246 0 246 512 t-244 512q-124 0 -187 -130.5t-63 -381.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="1219" d="M84 735q0 363 136 556.5t392 193.5q253 0 388 -194.5t135 -557.5t-136 -558t-389 -195q-254 0 -390 194.5t-136 560.5zM362 733q0 -250 63 -381t185 -131q246 0 246 512t-244 512q-124 0 -187 -130.5t-63 -381.5zM477 1579v27q41 53 95 151t77 150h318v-21 q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xd4;" horiz-adv-x="1219" d="M84 735q0 363 136 556.5t392 193.5q253 0 388 -194.5t135 -557.5t-136 -558t-389 -195q-254 0 -390 194.5t-136 560.5zM252 1579v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168zM362 733q0 -250 63 -381t185 -131 q246 0 246 512t-244 512q-124 0 -187 -130.5t-63 -381.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="1219" d="M84 735q0 363 136 556.5t392 193.5q253 0 388 -194.5t135 -557.5t-136 -558t-389 -195q-254 0 -390 194.5t-136 560.5zM272 1577q11 153 68.5 227t150.5 74q43 0 77.5 -16.5t64.5 -36.5t58 -36.5t58 -16.5q34 0 49.5 26t26.5 82h141q-11 -152 -69.5 -226.5t-149.5 -74.5 q-44 0 -78.5 16.5t-64 37t-56.5 37t-57 16.5q-31 0 -49.5 -23.5t-28.5 -85.5h-141zM362 733q0 -250 63 -381t185 -131q246 0 246 512t-244 512q-124 0 -187 -130.5t-63 -381.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1219" d="M84 735q0 363 136 556.5t392 193.5q253 0 388 -194.5t135 -557.5t-136 -558t-389 -195q-254 0 -390 194.5t-136 560.5zM276 1741q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM362 733q0 -250 63 -381t185 -131q246 0 246 512 t-244 512q-124 0 -187 -130.5t-63 -381.5zM674 1741q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="918" d="M92 389l246 334l-246 330l152 112l213 -282l215 282l151 -112l-245 -330l245 -334l-151 -113l-215 289l-213 -289z" />
<glyph unicode="&#xd8;" horiz-adv-x="1219" d="M84 735q0 363 136 556.5t392 193.5q137 0 252 -68l72 142l166 -76l-101 -197q134 -192 134 -553q0 -363 -136 -558t-389 -195q-135 0 -241 57l-70 -139l-166 77l96 187q-145 193 -145 573zM362 733q0 -158 21 -266l371 729q-53 49 -142 49q-124 0 -187 -130.5t-63 -381.5 zM481 260q48 -39 129 -39q246 0 246 512q0 152 -12 236z" />
<glyph unicode="&#xd9;" horiz-adv-x="1151" d="M123 520v942h270v-962q0 -279 185 -279q86 0 133 71t47 202v968h270v-946q0 -261 -118 -398.5t-341 -137.5q-218 0 -332 136.5t-114 403.5zM210 1886v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5z" />
<glyph unicode="&#xda;" horiz-adv-x="1151" d="M123 520v942h270v-962q0 -279 185 -279q86 0 133 71t47 202v968h270v-946q0 -261 -118 -398.5t-341 -137.5q-218 0 -332 136.5t-114 403.5zM444 1579v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xdb;" horiz-adv-x="1151" d="M123 520v942h270v-962q0 -279 185 -279q86 0 133 71t47 202v968h270v-946q0 -261 -118 -398.5t-341 -137.5q-218 0 -332 136.5t-114 403.5zM219 1579v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168z" />
<glyph unicode="&#xdc;" horiz-adv-x="1151" d="M123 520v942h270v-962q0 -279 185 -279q86 0 133 71t47 202v968h270v-946q0 -261 -118 -398.5t-341 -137.5q-218 0 -332 136.5t-114 403.5zM243 1741q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM641 1741q0 60 35 94.5 t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="961" d="M0 1462h287l194 -577l195 577h285l-347 -876v-586h-268v575zM346 1579v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xde;" horiz-adv-x="981" d="M129 0v1462h272v-233h97q428 0 428 -451q0 -233 -117 -358t-340 -125h-68v-295h-272zM401 530h52q100 0 149 56t49 182q0 227 -182 227h-68v-465z" />
<glyph unicode="&#xdf;" horiz-adv-x="1108" d="M113 0v1151q0 202 112.5 309t319.5 107q196 0 307 -85t111 -237q0 -151 -106 -263l-51 -53q-56 -58 -56 -93q0 -36 26.5 -69t126.5 -114q95 -76 128.5 -146.5t33.5 -168.5q0 -169 -100 -263.5t-283 -94.5q-140 0 -231 53v225q105 -70 208 -70q134 0 134 129 q0 55 -27 95.5t-115 109.5q-89 66 -127.5 129t-38.5 148q0 62 25 113.5t79 104.5q58 57 80.5 96.5t22.5 86.5q0 68 -38.5 107t-112.5 39q-162 0 -162 -203v-1143h-266z" />
<glyph unicode="&#xe0;" horiz-adv-x="956" d="M55 328q0 176 96.5 261.5t288.5 94.5l148 6v80q0 156 -135 156q-100 0 -230 -78l-92 184q166 105 361 105q177 0 269.5 -101.5t92.5 -287.5v-748h-195l-53 152h-6q-59 -98 -122 -135t-159 -37q-123 0 -193.5 92t-70.5 256zM146 1548v21h318q27 -60 80.5 -155.5 t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5zM330 326q0 -138 98 -138q73 0 116.5 65.5t43.5 174.5v94l-82 -4q-91 -5 -133.5 -52t-42.5 -140z" />
<glyph unicode="&#xe1;" horiz-adv-x="956" d="M55 328q0 176 96.5 261.5t288.5 94.5l148 6v80q0 156 -135 156q-100 0 -230 -78l-92 184q166 105 361 105q177 0 269.5 -101.5t92.5 -287.5v-748h-195l-53 152h-6q-59 -98 -122 -135t-159 -37q-123 0 -193.5 92t-70.5 256zM330 326q0 -138 98 -138q73 0 116.5 65.5 t43.5 174.5v94l-82 -4q-91 -5 -133.5 -52t-42.5 -140zM362 1241v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xe2;" horiz-adv-x="956" d="M55 328q0 176 96.5 261.5t288.5 94.5l148 6v80q0 156 -135 156q-100 0 -230 -78l-92 184q166 105 361 105q177 0 269.5 -101.5t92.5 -287.5v-748h-195l-53 152h-6q-59 -98 -122 -135t-159 -37q-123 0 -193.5 92t-70.5 256zM140 1240v27q177 223 217 301h277 q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168zM330 326q0 -138 98 -138q73 0 116.5 65.5t43.5 174.5v94l-82 -4q-91 -5 -133.5 -52t-42.5 -140z" />
<glyph unicode="&#xe3;" horiz-adv-x="956" d="M55 328q0 176 96.5 261.5t288.5 94.5l148 6v80q0 156 -135 156q-100 0 -230 -78l-92 184q166 105 361 105q177 0 269.5 -101.5t92.5 -287.5v-748h-195l-53 152h-6q-59 -98 -122 -135t-159 -37q-123 0 -193.5 92t-70.5 256zM161 1239q11 153 68.5 227t150.5 74 q43 0 77.5 -16.5t64.5 -36.5t58 -36.5t58 -16.5q34 0 49.5 26t26.5 82h141q-11 -152 -69.5 -226.5t-149.5 -74.5q-44 0 -78.5 16.5t-64 37t-56.5 37t-57 16.5q-31 0 -49.5 -23.5t-28.5 -85.5h-141zM330 326q0 -138 98 -138q73 0 116.5 65.5t43.5 174.5v94l-82 -4 q-91 -5 -133.5 -52t-42.5 -140z" />
<glyph unicode="&#xe4;" horiz-adv-x="956" d="M55 328q0 176 96.5 261.5t288.5 94.5l148 6v80q0 156 -135 156q-100 0 -230 -78l-92 184q166 105 361 105q177 0 269.5 -101.5t92.5 -287.5v-748h-195l-53 152h-6q-59 -98 -122 -135t-159 -37q-123 0 -193.5 92t-70.5 256zM164 1403q0 61 35 95t97 34t97.5 -35.5 t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM330 326q0 -138 98 -138q73 0 116.5 65.5t43.5 174.5v94l-82 -4q-91 -5 -133.5 -52t-42.5 -140zM562 1403q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="956" d="M55 328q0 176 96.5 261.5t288.5 94.5l148 6v80q0 156 -135 156q-100 0 -230 -78l-92 184q166 105 361 105q177 0 269.5 -101.5t92.5 -287.5v-748h-195l-53 152h-6q-59 -98 -122 -135t-159 -37q-123 0 -193.5 92t-70.5 256zM249 1473q0 108 67 170.5t177 62.5 q109 0 179.5 -64t70.5 -167q0 -107 -69.5 -171.5t-180.5 -64.5q-110 0 -177 62.5t-67 171.5zM330 326q0 -138 98 -138q73 0 116.5 65.5t43.5 174.5v94l-82 -4q-91 -5 -133.5 -52t-42.5 -140zM393 1473q0 -47 25 -74t75 -27q43 0 71.5 27t28.5 74q0 46 -28.5 73t-71.5 27 t-71.5 -27t-28.5 -73z" />
<glyph unicode="&#xe6;" horiz-adv-x="1446" d="M55 328q0 176 96.5 261.5t288.5 94.5l148 6v78q0 158 -135 158q-100 0 -230 -78l-92 184q166 105 361 105q165 0 258 -93q99 91 241 91q184 0 287.5 -134t103.5 -370v-142h-534q3 -149 61 -221.5t160 -72.5q135 0 266 81v-219q-127 -77 -307 -77q-231 0 -344 180 q-57 -93 -132.5 -136.5t-180.5 -43.5q-154 0 -235 88.5t-81 259.5zM330 326q0 -138 98 -138q73 0 116.5 65.5t43.5 174.5v94l-82 -4q-91 -5 -133.5 -52t-42.5 -140zM854 682h270q-2 122 -37 185t-96 63q-125 0 -137 -248z" />
<glyph unicode="&#xe7;" horiz-adv-x="760" d="M74 551q0 294 98.5 439t304.5 145q62 0 130 -18t122 -52l-76 -207q-83 49 -147 49q-85 0 -122.5 -88.5t-37.5 -265.5q0 -173 37 -258.5t121 -85.5q100 0 209 70v-234q-105 -65 -232 -65q-199 0 -303 145t-104 426zM221 -317q61 -17 121 -17q88 0 88 66q0 84 -157 110 l79 158h173l-37 -76q68 -17 111.5 -63t43.5 -121q0 -107 -71 -169.5t-220 -62.5q-73 0 -131 21v154z" />
<glyph unicode="&#xe8;" horiz-adv-x="942" d="M74 549q0 276 109 431t304 155q182 0 287 -132.5t105 -371.5v-142h-535q3 -149 61 -221.5t160 -72.5q130 0 266 81v-219q-128 -77 -307 -77q-209 0 -329.5 148.5t-120.5 420.5zM144 1548v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5z M348 682h270q-2 122 -37 185t-96 63q-57 0 -94 -60.5t-43 -187.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="942" d="M74 549q0 276 109 431t304 155q182 0 287 -132.5t105 -371.5v-142h-535q3 -149 61 -221.5t160 -72.5q130 0 266 81v-219q-128 -77 -307 -77q-209 0 -329.5 148.5t-120.5 420.5zM348 682h270q-2 122 -37 185t-96 63q-57 0 -94 -60.5t-43 -187.5zM348 1241v27q41 53 95 151 t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xea;" horiz-adv-x="942" d="M74 549q0 276 109 431t304 155q182 0 287 -132.5t105 -371.5v-142h-535q3 -149 61 -221.5t160 -72.5q130 0 266 81v-219q-128 -77 -307 -77q-209 0 -329.5 148.5t-120.5 420.5zM132 1241v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178 q-89 -100 -186 -178h-168zM348 682h270q-2 122 -37 185t-96 63q-57 0 -94 -60.5t-43 -187.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="942" d="M74 549q0 276 109 431t304 155q182 0 287 -132.5t105 -371.5v-142h-535q3 -149 61 -221.5t160 -72.5q130 0 266 81v-219q-128 -77 -307 -77q-209 0 -329.5 148.5t-120.5 420.5zM156 1403q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5 t-35 93.5zM348 682h270q-2 122 -37 185t-96 63q-57 0 -94 -60.5t-43 -187.5zM554 1403q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xec;" horiz-adv-x="496" d="M-110 1548v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5zM115 0v1114h266v-1114h-266z" />
<glyph unicode="&#xed;" horiz-adv-x="496" d="M113 1241v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187zM115 0v1114h266v-1114h-266z" />
<glyph unicode="&#xee;" horiz-adv-x="496" d="M-110 1241v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168zM115 0v1114h266v-1114h-266z" />
<glyph unicode="&#xef;" horiz-adv-x="496" d="M-85 1403q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM115 0v1114h266v-1114h-266zM313 1403q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1012" d="M74 483q0 240 100.5 374t284.5 134q121 0 205 -117l12 3q-48 178 -182 317l-220 -145l-96 145l193 123q-49 38 -154 96l94 158q148 -77 234 -144l207 140l94 -144l-170 -110q135 -141 198.5 -325.5t63.5 -418.5q0 -275 -114.5 -432t-321.5 -157q-198 0 -313 135.5 t-115 367.5zM346 483q0 -133 41.5 -204.5t120.5 -71.5q85 0 121.5 81t36.5 242q0 116 -42.5 183t-113.5 67q-88 0 -126 -74t-38 -223z" />
<glyph unicode="&#xf1;" horiz-adv-x="1038" d="M113 0v1114h211l32 -143h17q36 77 103 120.5t157 43.5q152 0 227.5 -104t75.5 -304v-727h-268v664q0 121 -29 183t-94 62q-86 0 -126 -85.5t-40 -288.5v-535h-266zM183 1239q11 153 68.5 227t150.5 74q43 0 77.5 -16.5t64.5 -36.5t58 -36.5t58 -16.5q34 0 49.5 26 t26.5 82h141q-11 -152 -69.5 -226.5t-149.5 -74.5q-44 0 -78.5 16.5t-64 37t-56.5 37t-57 16.5q-31 0 -49.5 -23.5t-28.5 -85.5h-141z" />
<glyph unicode="&#xf2;" horiz-adv-x="1012" d="M74 559q0 271 115 423.5t319 152.5q197 0 313.5 -155.5t116.5 -420.5q0 -274 -116 -426.5t-318 -152.5q-128 0 -226 70t-151 201.5t-53 307.5zM156 1548v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5zM346 559q0 -174 38 -263t122 -89 q160 0 160 352q0 175 -38.5 261.5t-121.5 86.5q-84 0 -122 -86.5t-38 -261.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1012" d="M74 559q0 271 115 423.5t319 152.5q197 0 313.5 -155.5t116.5 -420.5q0 -274 -116 -426.5t-318 -152.5q-128 0 -226 70t-151 201.5t-53 307.5zM346 559q0 -174 38 -263t122 -89q160 0 160 352q0 175 -38.5 261.5t-121.5 86.5q-84 0 -122 -86.5t-38 -261.5zM366 1241v27 q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xf4;" horiz-adv-x="1012" d="M74 559q0 271 115 423.5t319 152.5q197 0 313.5 -155.5t116.5 -420.5q0 -274 -116 -426.5t-318 -152.5q-128 0 -226 70t-151 201.5t-53 307.5zM151 1241v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168zM346 559 q0 -174 38 -263t122 -89q160 0 160 352q0 175 -38.5 261.5t-121.5 86.5q-84 0 -122 -86.5t-38 -261.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1012" d="M74 559q0 271 115 423.5t319 152.5q197 0 313.5 -155.5t116.5 -420.5q0 -274 -116 -426.5t-318 -152.5q-128 0 -226 70t-151 201.5t-53 307.5zM169 1239q11 153 68.5 227t150.5 74q43 0 77.5 -16.5t64.5 -36.5t58 -36.5t58 -16.5q34 0 49.5 26t26.5 82h141 q-11 -152 -69.5 -226.5t-149.5 -74.5q-44 0 -78.5 16.5t-64 37t-56.5 37t-57 16.5q-31 0 -49.5 -23.5t-28.5 -85.5h-141zM346 559q0 -174 38 -263t122 -89q160 0 160 352q0 175 -38.5 261.5t-121.5 86.5q-84 0 -122 -86.5t-38 -261.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1012" d="M74 559q0 271 115 423.5t319 152.5q197 0 313.5 -155.5t116.5 -420.5q0 -274 -116 -426.5t-318 -152.5q-128 0 -226 70t-151 201.5t-53 307.5zM175 1403q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM346 559q0 -174 38 -263 t122 -89q160 0 160 352q0 175 -38.5 261.5t-121.5 86.5q-84 0 -122 -86.5t-38 -261.5zM573 1403q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="918" d="M106 618v207h705v-207h-705zM332 373q0 145 125 145q61 0 92 -37.5t31 -107.5q0 -68 -32.5 -107t-90.5 -39q-54 0 -89.5 34.5t-35.5 111.5zM332 1071q0 146 125 146q61 0 92 -37.5t31 -108.5q0 -67 -32.5 -106t-90.5 -39q-54 0 -89.5 34t-35.5 111z" />
<glyph unicode="&#xf8;" horiz-adv-x="1012" d="M74 559q0 271 115 423.5t319 152.5q98 0 176 -41l51 96l160 -74l-72 -141q115 -148 115 -416q0 -274 -116 -426.5t-318 -152.5q-92 0 -166 36l-57 -112l-162 76l78 151q-57 71 -90 179.5t-33 248.5zM338 486l2 -70l244 475q-34 16 -78 16q-168 0 -168 -348v-73zM438 219 q25 -12 68 -12q83 0 126.5 89t43.5 263l-4 117z" />
<glyph unicode="&#xf9;" horiz-adv-x="1038" d="M102 387v727h269v-663q0 -122 29 -184t94 -62q86 0 125.5 86t39.5 289v534h269v-1114h-213l-33 143h-14q-37 -76 -105.5 -119.5t-156.5 -43.5q-150 0 -227 102t-77 305zM160 1548v21h318q27 -60 80.5 -155.5t91.5 -145.5v-27h-187q-75 60 -176 160.5t-127 146.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="1038" d="M102 387v727h269v-663q0 -122 29 -184t94 -62q86 0 125.5 86t39.5 289v534h269v-1114h-213l-33 143h-14q-37 -76 -105.5 -119.5t-156.5 -43.5q-150 0 -227 102t-77 305zM381 1241v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xfb;" horiz-adv-x="1038" d="M102 387v727h269v-663q0 -122 29 -184t94 -62q86 0 125.5 86t39.5 289v534h269v-1114h-213l-33 143h-14q-37 -76 -105.5 -119.5t-156.5 -43.5q-150 0 -227 102t-77 305zM163 1241v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178 h-168z" />
<glyph unicode="&#xfc;" horiz-adv-x="1038" d="M102 387v727h269v-663q0 -122 29 -184t94 -62q86 0 125.5 86t39.5 289v534h269v-1114h-213l-33 143h-14q-37 -76 -105.5 -119.5t-156.5 -43.5q-150 0 -227 102t-77 305zM185 1403q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5z M583 1403q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="913" d="M0 1114h276l154 -629q23 -84 37 -235h6q1 24 9 85.5t13 96.5t138 682h280l-319 -1194q-58 -220 -154 -316t-256 -96q-79 0 -141 17v223q41 -12 92 -12q134 0 176 176l19 70zM310 1241v27q41 53 95 151t77 150h318v-21q-24 -43 -119 -138.5t-184 -168.5h-187z" />
<glyph unicode="&#xfe;" horiz-adv-x="1016" d="M113 -492v2048h266v-364q0 -23 -1 -48t-13 -175h14q49 93 106 129.5t133 36.5q152 0 238 -150.5t86 -425.5q0 -271 -89 -425t-245 -154q-70 0 -126 33t-103 114h-14q16 -136 16 -164v-455h-268zM379 561q0 -184 35 -268t116 -84q140 0 140 354q0 174 -34.5 260t-107.5 86 q-78 0 -112.5 -72.5t-36.5 -240.5v-35z" />
<glyph unicode="&#xff;" horiz-adv-x="913" d="M0 1114h276l154 -629q23 -84 37 -235h6q1 24 9 85.5t13 96.5t138 682h280l-319 -1194q-58 -220 -154 -316t-256 -96q-79 0 -141 17v223q41 -12 92 -12q134 0 176 176l19 70zM125 1403q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5 t-35 93.5zM523 1403q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1446" d="M84 735q0 363 136 556.5t392 193.5q81 0 158 -23h590v-235h-383v-348h354v-236h-354v-405h383v-238h-600q-76 -20 -150 -20q-254 0 -390 194.5t-136 560.5zM362 733q0 -250 63 -381t185 -131q54 0 95 15v995q-47 14 -93 14q-124 0 -187 -130.5t-63 -381.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1532" d="M74 559q0 271 115 423.5t319 152.5q174 0 289 -140q107 140 282 140q182 0 285.5 -133.5t103.5 -370.5v-142h-532q3 -149 61 -221.5t158 -72.5q132 0 268 81v-219q-130 -77 -307 -77q-197 0 -313 141q-116 -141 -299 -141q-128 0 -226 70t-151 201.5t-53 307.5zM346 559 q0 -174 38 -263t122 -89q160 0 160 352q0 175 -38.5 261.5t-121.5 86.5q-84 0 -122 -86.5t-38 -261.5zM940 682h270q-2 122 -37 185t-96 63q-57 0 -94 -60.5t-43 -187.5z" />
<glyph unicode="&#x178;" horiz-adv-x="961" d="M0 1462h287l194 -577l195 577h285l-347 -876v-586h-268v575zM150 1741q0 61 35 95t97 34t97.5 -35.5t35.5 -93.5q0 -57 -35.5 -92t-97.5 -35t-97 33.5t-35 93.5zM548 1741q0 60 35 94.5t98 34.5q61 0 97 -34t36 -95q0 -59 -35.5 -93t-97.5 -34t-97.5 34.5t-35.5 92.5z " />
<glyph unicode="&#x2c6;" horiz-adv-x="1135" d="M211 1241v27q177 223 217 301h277q47 -87 217 -301v-27h-168q-96 72 -189 178q-89 -100 -186 -178h-168z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1135" d="M221 1239q11 153 68.5 227t150.5 74q43 0 77.5 -16.5t64.5 -36.5t58 -36.5t58 -16.5q34 0 49.5 26t26.5 82h141q-11 -152 -69.5 -226.5t-149.5 -74.5q-44 0 -78.5 16.5t-64 37t-56.5 37t-57 16.5q-31 0 -49.5 -23.5t-28.5 -85.5h-141z" />
<glyph unicode="&#x2000;" horiz-adv-x="953" />
<glyph unicode="&#x2001;" horiz-adv-x="1907" />
<glyph unicode="&#x2002;" horiz-adv-x="953" />
<glyph unicode="&#x2003;" horiz-adv-x="1907" />
<glyph unicode="&#x2004;" horiz-adv-x="635" />
<glyph unicode="&#x2005;" horiz-adv-x="476" />
<glyph unicode="&#x2006;" horiz-adv-x="317" />
<glyph unicode="&#x2007;" horiz-adv-x="317" />
<glyph unicode="&#x2008;" horiz-adv-x="238" />
<glyph unicode="&#x2009;" horiz-adv-x="381" />
<glyph unicode="&#x200a;" horiz-adv-x="105" />
<glyph unicode="&#x2010;" horiz-adv-x="647" d="M72 432v234h503v-234h-503z" />
<glyph unicode="&#x2011;" horiz-adv-x="647" d="M72 432v234h503v-234h-503z" />
<glyph unicode="&#x2012;" horiz-adv-x="647" d="M72 432v234h503v-234h-503z" />
<glyph unicode="&#x2013;" horiz-adv-x="973" d="M70 442v217h833v-217h-833z" />
<glyph unicode="&#x2014;" horiz-adv-x="1579" d="M70 442v217h1437v-217h-1437z" />
<glyph unicode="&#x2018;" horiz-adv-x="420" d="M20 983q25 97 73 228t104 251h202q-31 -125 -61 -277t-39 -224h-264z" />
<glyph unicode="&#x2019;" horiz-adv-x="420" d="M20 961q64 256 101 501h264l14 -22q-50 -197 -176 -479h-203z" />
<glyph unicode="&#x201a;" horiz-adv-x="553" d="M51 -264q65 266 101 502h264l14 -23q-52 -202 -176 -479h-203z" />
<glyph unicode="&#x201c;" horiz-adv-x="854" d="M20 983q25 97 73 228t104 251h202q-31 -125 -61 -277t-39 -224h-264zM455 983q31 118 83 254.5t93 224.5h203q-66 -267 -101 -501h-264z" />
<glyph unicode="&#x201d;" horiz-adv-x="854" d="M20 961q64 256 101 501h264l14 -22q-50 -197 -176 -479h-203zM455 961q62 246 100 501h264l15 -22q-49 -192 -177 -479h-202z" />
<glyph unicode="&#x201e;" horiz-adv-x="987" d="M51 -264q65 266 101 502h264l14 -23q-52 -202 -176 -479h-203zM485 -264q65 266 101 502h264l14 -23q-52 -202 -176 -479h-203z" />
<glyph unicode="&#x2022;" horiz-adv-x="750" d="M98 748q0 148 71.5 227.5t205.5 79.5q132 0 204 -80.5t72 -226.5q0 -145 -73 -226.5t-203 -81.5q-133 0 -205 81t-72 227z" />
<glyph unicode="&#x2026;" horiz-adv-x="1659" d="M111 137q0 78 41.5 121t123.5 43t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5zM664 137q0 78 41.5 121t123.5 43t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5zM1217 137q0 78 41.5 121t123.5 43 t124 -42.5t42 -121.5q0 -78 -43.5 -121t-122.5 -43q-81 0 -123 43.5t-42 120.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="381" />
<glyph unicode="&#x2039;" horiz-adv-x="653" d="M74 543v26l307 447l199 -111l-220 -348l220 -348l-199 -111z" />
<glyph unicode="&#x203a;" horiz-adv-x="653" d="M74 209l219 348l-219 348l198 111l308 -447v-26l-308 -445z" />
<glyph unicode="&#x205f;" horiz-adv-x="476" />
<glyph unicode="&#x20ac;" d="M41 487v168h117v123h-117v168h129q38 263 162 401t315 138q87 0 151.5 -16.5t129.5 -53.5l-90 -215q-96 47 -179 47q-85 0 -140.5 -77.5t-78.5 -223.5h295v-168h-309v-123h264v-168h-248q51 -268 232 -268q108 0 233 74v-242q-109 -71 -262 -71q-194 0 -315 129.5 t-158 377.5h-131z" />
<glyph unicode="&#x2122;" horiz-adv-x="1366" d="M12 1321v141h494v-141h-170v-580h-154v580h-170zM553 741v721h242l106 -489l117 489h239v-721h-153v452l2 81h-4l-127 -533h-148l-122 533h-7l4 -189v-344h-149z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1115" d="M0 0v1115h1115v-1115h-1115z" />
<hkern u1="&#x22;" u2="&#x178;" k="-80" />
<hkern u1="&#x22;" u2="&#x153;" k="39" />
<hkern u1="&#x22;" u2="&#xff;" k="-41" />
<hkern u1="&#x22;" u2="&#xfd;" k="-41" />
<hkern u1="&#x22;" u2="&#xf8;" k="39" />
<hkern u1="&#x22;" u2="&#xf6;" k="39" />
<hkern u1="&#x22;" u2="&#xf5;" k="39" />
<hkern u1="&#x22;" u2="&#xf4;" k="39" />
<hkern u1="&#x22;" u2="&#xf3;" k="39" />
<hkern u1="&#x22;" u2="&#xf2;" k="41" />
<hkern u1="&#x22;" u2="&#xeb;" k="39" />
<hkern u1="&#x22;" u2="&#xea;" k="39" />
<hkern u1="&#x22;" u2="&#xe9;" k="39" />
<hkern u1="&#x22;" u2="&#xe8;" k="39" />
<hkern u1="&#x22;" u2="&#xe7;" k="39" />
<hkern u1="&#x22;" u2="&#xe6;" k="41" />
<hkern u1="&#x22;" u2="&#xdd;" k="-80" />
<hkern u1="&#x22;" u2="&#xcf;" k="-41" />
<hkern u1="&#x22;" u2="&#xce;" k="-41" />
<hkern u1="&#x22;" u2="&#xcd;" k="-41" />
<hkern u1="&#x22;" u2="&#xcc;" k="-41" />
<hkern u1="&#x22;" u2="&#xc6;" k="141" />
<hkern u1="&#x22;" u2="&#xc5;" k="59" />
<hkern u1="&#x22;" u2="&#xc4;" k="59" />
<hkern u1="&#x22;" u2="&#xc3;" k="61" />
<hkern u1="&#x22;" u2="&#xc2;" k="61" />
<hkern u1="&#x22;" u2="&#xc1;" k="61" />
<hkern u1="&#x22;" u2="&#xc0;" k="61" />
<hkern u1="&#x22;" u2="y" k="-41" />
<hkern u1="&#x22;" u2="v" k="-41" />
<hkern u1="&#x22;" u2="t" k="-41" />
<hkern u1="&#x22;" u2="q" k="41" />
<hkern u1="&#x22;" u2="o" k="41" />
<hkern u1="&#x22;" u2="g" k="20" />
<hkern u1="&#x22;" u2="e" k="41" />
<hkern u1="&#x22;" u2="d" k="41" />
<hkern u1="&#x22;" u2="c" k="41" />
<hkern u1="&#x22;" u2="Z" k="-41" />
<hkern u1="&#x22;" u2="Y" k="-82" />
<hkern u1="&#x22;" u2="X" k="-41" />
<hkern u1="&#x22;" u2="W" k="-102" />
<hkern u1="&#x22;" u2="V" k="-82" />
<hkern u1="&#x22;" u2="T" k="-82" />
<hkern u1="&#x22;" u2="I" k="-41" />
<hkern u1="&#x22;" u2="A" k="61" />
<hkern u1="&#x27;" u2="&#x178;" k="-80" />
<hkern u1="&#x27;" u2="&#x153;" k="39" />
<hkern u1="&#x27;" u2="&#xff;" k="-41" />
<hkern u1="&#x27;" u2="&#xfd;" k="-41" />
<hkern u1="&#x27;" u2="&#xf8;" k="39" />
<hkern u1="&#x27;" u2="&#xf6;" k="39" />
<hkern u1="&#x27;" u2="&#xf5;" k="39" />
<hkern u1="&#x27;" u2="&#xf4;" k="39" />
<hkern u1="&#x27;" u2="&#xf3;" k="39" />
<hkern u1="&#x27;" u2="&#xf2;" k="41" />
<hkern u1="&#x27;" u2="&#xeb;" k="39" />
<hkern u1="&#x27;" u2="&#xea;" k="39" />
<hkern u1="&#x27;" u2="&#xe9;" k="39" />
<hkern u1="&#x27;" u2="&#xe8;" k="39" />
<hkern u1="&#x27;" u2="&#xe7;" k="39" />
<hkern u1="&#x27;" u2="&#xe6;" k="41" />
<hkern u1="&#x27;" u2="&#xdd;" k="-80" />
<hkern u1="&#x27;" u2="&#xcf;" k="-41" />
<hkern u1="&#x27;" u2="&#xce;" k="-41" />
<hkern u1="&#x27;" u2="&#xcd;" k="-41" />
<hkern u1="&#x27;" u2="&#xcc;" k="-41" />
<hkern u1="&#x27;" u2="&#xc6;" k="141" />
<hkern u1="&#x27;" u2="&#xc5;" k="59" />
<hkern u1="&#x27;" u2="&#xc4;" k="59" />
<hkern u1="&#x27;" u2="&#xc3;" k="61" />
<hkern u1="&#x27;" u2="&#xc2;" k="61" />
<hkern u1="&#x27;" u2="&#xc1;" k="61" />
<hkern u1="&#x27;" u2="&#xc0;" k="61" />
<hkern u1="&#x27;" u2="y" k="-41" />
<hkern u1="&#x27;" u2="v" k="-41" />
<hkern u1="&#x27;" u2="t" k="-41" />
<hkern u1="&#x27;" u2="q" k="41" />
<hkern u1="&#x27;" u2="o" k="41" />
<hkern u1="&#x27;" u2="g" k="20" />
<hkern u1="&#x27;" u2="e" k="41" />
<hkern u1="&#x27;" u2="d" k="41" />
<hkern u1="&#x27;" u2="c" k="41" />
<hkern u1="&#x27;" u2="Z" k="-41" />
<hkern u1="&#x27;" u2="Y" k="-82" />
<hkern u1="&#x27;" u2="X" k="-41" />
<hkern u1="&#x27;" u2="W" k="-102" />
<hkern u1="&#x27;" u2="V" k="-82" />
<hkern u1="&#x27;" u2="T" k="-82" />
<hkern u1="&#x27;" u2="I" k="-41" />
<hkern u1="&#x27;" u2="A" k="61" />
<hkern u1="&#x28;" u2="J" k="-123" />
<hkern u1="&#x2c;" u2="&#x178;" k="80" />
<hkern u1="&#x2c;" u2="&#xdd;" k="80" />
<hkern u1="&#x2c;" u2="&#xdc;" k="18" />
<hkern u1="&#x2c;" u2="&#xdb;" k="18" />
<hkern u1="&#x2c;" u2="&#xda;" k="18" />
<hkern u1="&#x2c;" u2="&#xd9;" k="18" />
<hkern u1="&#x2c;" u2="&#xd8;" k="41" />
<hkern u1="&#x2c;" u2="&#xd6;" k="41" />
<hkern u1="&#x2c;" u2="&#xd5;" k="41" />
<hkern u1="&#x2c;" u2="&#xd4;" k="41" />
<hkern u1="&#x2c;" u2="&#xd3;" k="41" />
<hkern u1="&#x2c;" u2="&#xd2;" k="39" />
<hkern u1="&#x2c;" u2="&#xc7;" k="39" />
<hkern u1="&#x2c;" u2="Y" k="82" />
<hkern u1="&#x2c;" u2="W" k="61" />
<hkern u1="&#x2c;" u2="V" k="82" />
<hkern u1="&#x2c;" u2="U" k="20" />
<hkern u1="&#x2c;" u2="T" k="82" />
<hkern u1="&#x2c;" u2="Q" k="41" />
<hkern u1="&#x2c;" u2="O" k="41" />
<hkern u1="&#x2c;" u2="G" k="41" />
<hkern u1="&#x2c;" u2="C" k="41" />
<hkern u1="&#x2d;" u2="T" k="82" />
<hkern u1="&#x2e;" u2="&#x178;" k="80" />
<hkern u1="&#x2e;" u2="&#xdd;" k="80" />
<hkern u1="&#x2e;" u2="&#xdc;" k="18" />
<hkern u1="&#x2e;" u2="&#xdb;" k="18" />
<hkern u1="&#x2e;" u2="&#xda;" k="18" />
<hkern u1="&#x2e;" u2="&#xd9;" k="18" />
<hkern u1="&#x2e;" u2="&#xd8;" k="41" />
<hkern u1="&#x2e;" u2="&#xd6;" k="41" />
<hkern u1="&#x2e;" u2="&#xd5;" k="41" />
<hkern u1="&#x2e;" u2="&#xd4;" k="41" />
<hkern u1="&#x2e;" u2="&#xd3;" k="41" />
<hkern u1="&#x2e;" u2="&#xd2;" k="39" />
<hkern u1="&#x2e;" u2="&#xc7;" k="39" />
<hkern u1="&#x2e;" u2="Y" k="82" />
<hkern u1="&#x2e;" u2="W" k="61" />
<hkern u1="&#x2e;" u2="V" k="82" />
<hkern u1="&#x2e;" u2="U" k="20" />
<hkern u1="&#x2e;" u2="T" k="82" />
<hkern u1="&#x2e;" u2="Q" k="41" />
<hkern u1="&#x2e;" u2="O" k="41" />
<hkern u1="&#x2e;" u2="G" k="41" />
<hkern u1="&#x2e;" u2="C" k="41" />
<hkern u1="A" u2="&#x201d;" k="39" />
<hkern u1="A" u2="&#x2019;" k="39" />
<hkern u1="A" u2="&#x178;" k="55" />
<hkern u1="A" u2="&#x152;" k="31" />
<hkern u1="A" u2="&#xdd;" k="55" />
<hkern u1="A" u2="Y" k="35" />
<hkern u1="A" u2="W" k="31" />
<hkern u1="A" u2="V" k="27" />
<hkern u1="A" u2="T" k="70" />
<hkern u1="A" u2="&#x27;" k="41" />
<hkern u1="A" u2="&#x22;" k="41" />
<hkern u1="C" u2="&#x201d;" k="-31" />
<hkern u1="C" u2="&#x2019;" k="-31" />
<hkern u1="C" u2="&#xd8;" k="31" />
<hkern u1="C" u2="&#xd6;" k="31" />
<hkern u1="C" u2="&#xd5;" k="31" />
<hkern u1="C" u2="&#xd4;" k="31" />
<hkern u1="C" u2="&#xd3;" k="31" />
<hkern u1="C" u2="&#xd2;" k="31" />
<hkern u1="C" u2="&#xc7;" k="31" />
<hkern u1="C" u2="&#x7d;" k="-31" />
<hkern u1="C" u2="]" k="-31" />
<hkern u1="C" u2="Q" k="31" />
<hkern u1="C" u2="O" k="31" />
<hkern u1="C" u2="G" k="31" />
<hkern u1="C" u2="C" k="31" />
<hkern u1="C" u2="&#x29;" k="-31" />
<hkern u1="C" u2="&#x27;" k="-31" />
<hkern u1="C" u2="&#x22;" k="-31" />
<hkern u1="D" u2="&#x178;" k="31" />
<hkern u1="D" u2="&#xdd;" k="31" />
<hkern u1="D" u2="&#xc6;" k="25" />
<hkern u1="D" u2="Y" k="31" />
<hkern u1="D" u2="T" k="27" />
<hkern u1="D" u2="&#x2e;" k="27" />
<hkern u1="D" u2="&#x2c;" k="27" />
<hkern u1="F" u2="&#x201d;" k="-43" />
<hkern u1="F" u2="&#x2019;" k="-43" />
<hkern u1="F" u2="&#xc6;" k="25" />
<hkern u1="F" u2="&#xc5;" k="31" />
<hkern u1="F" u2="&#xc4;" k="31" />
<hkern u1="F" u2="&#xc3;" k="31" />
<hkern u1="F" u2="&#xc2;" k="31" />
<hkern u1="F" u2="&#xc1;" k="31" />
<hkern u1="F" u2="&#xc0;" k="31" />
<hkern u1="F" u2="&#x7d;" k="-31" />
<hkern u1="F" u2="]" k="-31" />
<hkern u1="F" u2="A" k="31" />
<hkern u1="F" u2="&#x3f;" k="-31" />
<hkern u1="F" u2="&#x2e;" k="57" />
<hkern u1="F" u2="&#x2c;" k="57" />
<hkern u1="F" u2="&#x29;" k="-31" />
<hkern u1="F" u2="&#x27;" k="-45" />
<hkern u1="F" u2="&#x22;" k="-45" />
<hkern u1="I" u2="&#x201d;" k="-41" />
<hkern u1="I" u2="&#x2019;" k="-41" />
<hkern u1="I" u2="&#x27;" k="-41" />
<hkern u1="I" u2="&#x22;" k="-41" />
<hkern u1="K" u2="&#x201d;" k="-31" />
<hkern u1="K" u2="&#x2019;" k="-31" />
<hkern u1="K" u2="&#x152;" k="31" />
<hkern u1="K" u2="&#xd8;" k="31" />
<hkern u1="K" u2="&#xd6;" k="31" />
<hkern u1="K" u2="&#xd5;" k="31" />
<hkern u1="K" u2="&#xd4;" k="31" />
<hkern u1="K" u2="&#xd3;" k="31" />
<hkern u1="K" u2="&#xd2;" k="31" />
<hkern u1="K" u2="&#xc7;" k="31" />
<hkern u1="K" u2="Q" k="31" />
<hkern u1="K" u2="O" k="31" />
<hkern u1="K" u2="G" k="31" />
<hkern u1="K" u2="C" k="31" />
<hkern u1="K" u2="&#x27;" k="-31" />
<hkern u1="K" u2="&#x22;" k="-31" />
<hkern u1="L" u2="&#x201d;" k="55" />
<hkern u1="L" u2="&#x2019;" k="55" />
<hkern u1="L" u2="&#x178;" k="55" />
<hkern u1="L" u2="&#xdd;" k="55" />
<hkern u1="L" u2="Y" k="57" />
<hkern u1="L" u2="W" k="27" />
<hkern u1="L" u2="V" k="41" />
<hkern u1="L" u2="T" k="70" />
<hkern u1="L" u2="&#x27;" k="57" />
<hkern u1="L" u2="&#x22;" k="57" />
<hkern u1="O" u2="&#x178;" k="31" />
<hkern u1="O" u2="&#xdd;" k="31" />
<hkern u1="O" u2="&#xc6;" k="31" />
<hkern u1="O" u2="Y" k="31" />
<hkern u1="O" u2="X" k="31" />
<hkern u1="O" u2="V" k="31" />
<hkern u1="O" u2="T" k="27" />
<hkern u1="O" u2="&#x2e;" k="27" />
<hkern u1="O" u2="&#x2c;" k="27" />
<hkern u1="P" u2="&#xc6;" k="84" />
<hkern u1="P" u2="&#xc5;" k="39" />
<hkern u1="P" u2="&#xc4;" k="39" />
<hkern u1="P" u2="&#xc3;" k="41" />
<hkern u1="P" u2="&#xc2;" k="41" />
<hkern u1="P" u2="&#xc1;" k="41" />
<hkern u1="P" u2="&#xc0;" k="41" />
<hkern u1="P" u2="Z" k="31" />
<hkern u1="P" u2="A" k="41" />
<hkern u1="P" u2="&#x2e;" k="131" />
<hkern u1="P" u2="&#x2c;" k="131" />
<hkern u1="Q" u2="&#x178;" k="10" />
<hkern u1="Q" u2="&#xdd;" k="10" />
<hkern u1="Q" u2="&#xc6;" k="27" />
<hkern u1="Q" u2="Y" k="10" />
<hkern u1="Q" u2="X" k="10" />
<hkern u1="Q" u2="V" k="10" />
<hkern u1="Q" u2="T" k="27" />
<hkern u1="Q" u2="&#x2e;" k="27" />
<hkern u1="Q" u2="&#x2c;" k="27" />
<hkern u1="T" u2="&#x201d;" k="-59" />
<hkern u1="T" u2="&#x2019;" k="-59" />
<hkern u1="T" u2="&#x2014;" k="55" />
<hkern u1="T" u2="&#x2013;" k="39" />
<hkern u1="T" u2="&#x153;" k="70" />
<hkern u1="T" u2="&#x152;" k="25" />
<hkern u1="T" u2="&#xff;" k="27" />
<hkern u1="T" u2="&#xfd;" k="27" />
<hkern u1="T" u2="&#xfc;" k="41" />
<hkern u1="T" u2="&#xfb;" k="41" />
<hkern u1="T" u2="&#xfa;" k="41" />
<hkern u1="T" u2="&#xf9;" k="39" />
<hkern u1="T" u2="&#xf8;" k="70" />
<hkern u1="T" u2="&#xf6;" k="70" />
<hkern u1="T" u2="&#xf5;" k="70" />
<hkern u1="T" u2="&#xf4;" k="70" />
<hkern u1="T" u2="&#xf3;" k="70" />
<hkern u1="T" u2="&#xf2;" k="70" />
<hkern u1="T" u2="&#xf1;" k="39" />
<hkern u1="T" u2="&#xeb;" k="70" />
<hkern u1="T" u2="&#xea;" k="70" />
<hkern u1="T" u2="&#xe9;" k="70" />
<hkern u1="T" u2="&#xe8;" k="70" />
<hkern u1="T" u2="&#xe7;" k="70" />
<hkern u1="T" u2="&#xe6;" k="70" />
<hkern u1="T" u2="&#xe5;" k="70" />
<hkern u1="T" u2="&#xe4;" k="70" />
<hkern u1="T" u2="&#xe3;" k="70" />
<hkern u1="T" u2="&#xe2;" k="70" />
<hkern u1="T" u2="&#xe1;" k="70" />
<hkern u1="T" u2="&#xe0;" k="70" />
<hkern u1="T" u2="&#xd8;" k="25" />
<hkern u1="T" u2="&#xd6;" k="25" />
<hkern u1="T" u2="&#xd5;" k="25" />
<hkern u1="T" u2="&#xd4;" k="25" />
<hkern u1="T" u2="&#xd3;" k="27" />
<hkern u1="T" u2="&#xd2;" k="25" />
<hkern u1="T" u2="&#xc7;" k="25" />
<hkern u1="T" u2="&#xc6;" k="84" />
<hkern u1="T" u2="&#xc5;" k="70" />
<hkern u1="T" u2="&#xc4;" k="70" />
<hkern u1="T" u2="&#xc3;" k="72" />
<hkern u1="T" u2="&#xc2;" k="70" />
<hkern u1="T" u2="&#xc1;" k="70" />
<hkern u1="T" u2="&#xc0;" k="70" />
<hkern u1="T" u2="z" k="27" />
<hkern u1="T" u2="y" k="27" />
<hkern u1="T" u2="x" k="27" />
<hkern u1="T" u2="w" k="27" />
<hkern u1="T" u2="v" k="27" />
<hkern u1="T" u2="u" k="41" />
<hkern u1="T" u2="s" k="70" />
<hkern u1="T" u2="r" k="41" />
<hkern u1="T" u2="q" k="70" />
<hkern u1="T" u2="p" k="41" />
<hkern u1="T" u2="o" k="70" />
<hkern u1="T" u2="n" k="41" />
<hkern u1="T" u2="m" k="41" />
<hkern u1="T" u2="g" k="57" />
<hkern u1="T" u2="e" k="70" />
<hkern u1="T" u2="d" k="70" />
<hkern u1="T" u2="c" k="70" />
<hkern u1="T" u2="a" k="70" />
<hkern u1="T" u2="Q" k="27" />
<hkern u1="T" u2="O" k="27" />
<hkern u1="T" u2="G" k="27" />
<hkern u1="T" u2="C" k="27" />
<hkern u1="T" u2="A" k="70" />
<hkern u1="T" u2="&#x3f;" k="-31" />
<hkern u1="T" u2="&#x2e;" k="57" />
<hkern u1="T" u2="&#x2d;" k="57" />
<hkern u1="T" u2="&#x2c;" k="57" />
<hkern u1="T" u2="&#x27;" k="-61" />
<hkern u1="T" u2="&#x22;" k="-61" />
<hkern u1="U" u2="&#xc6;" k="31" />
<hkern u1="U" u2="&#x2e;" k="31" />
<hkern u1="U" u2="&#x2c;" k="31" />
<hkern u1="V" u2="&#x201d;" k="-59" />
<hkern u1="V" u2="&#x2019;" k="-59" />
<hkern u1="V" u2="&#x153;" k="25" />
<hkern u1="V" u2="&#x152;" k="31" />
<hkern u1="V" u2="&#xfc;" k="31" />
<hkern u1="V" u2="&#xfb;" k="31" />
<hkern u1="V" u2="&#xfa;" k="31" />
<hkern u1="V" u2="&#xf9;" k="31" />
<hkern u1="V" u2="&#xf8;" k="25" />
<hkern u1="V" u2="&#xf6;" k="27" />
<hkern u1="V" u2="&#xf5;" k="27" />
<hkern u1="V" u2="&#xf4;" k="27" />
<hkern u1="V" u2="&#xf3;" k="27" />
<hkern u1="V" u2="&#xf2;" k="27" />
<hkern u1="V" u2="&#xf1;" k="31" />
<hkern u1="V" u2="&#xeb;" k="25" />
<hkern u1="V" u2="&#xea;" k="25" />
<hkern u1="V" u2="&#xe9;" k="25" />
<hkern u1="V" u2="&#xe8;" k="25" />
<hkern u1="V" u2="&#xe7;" k="25" />
<hkern u1="V" u2="&#xe6;" k="27" />
<hkern u1="V" u2="&#xe5;" k="27" />
<hkern u1="V" u2="&#xe4;" k="27" />
<hkern u1="V" u2="&#xe3;" k="27" />
<hkern u1="V" u2="&#xe2;" k="27" />
<hkern u1="V" u2="&#xe1;" k="27" />
<hkern u1="V" u2="&#xe0;" k="27" />
<hkern u1="V" u2="&#xd8;" k="31" />
<hkern u1="V" u2="&#xd6;" k="31" />
<hkern u1="V" u2="&#xd5;" k="31" />
<hkern u1="V" u2="&#xd4;" k="31" />
<hkern u1="V" u2="&#xd3;" k="31" />
<hkern u1="V" u2="&#xd2;" k="31" />
<hkern u1="V" u2="&#xc7;" k="31" />
<hkern u1="V" u2="&#xc6;" k="70" />
<hkern u1="V" u2="&#xc5;" k="25" />
<hkern u1="V" u2="&#xc4;" k="25" />
<hkern u1="V" u2="&#xc3;" k="27" />
<hkern u1="V" u2="&#xc2;" k="27" />
<hkern u1="V" u2="&#xc1;" k="27" />
<hkern u1="V" u2="&#xc0;" k="27" />
<hkern u1="V" u2="u" k="31" />
<hkern u1="V" u2="s" k="31" />
<hkern u1="V" u2="r" k="31" />
<hkern u1="V" u2="q" k="27" />
<hkern u1="V" u2="p" k="31" />
<hkern u1="V" u2="o" k="27" />
<hkern u1="V" u2="n" k="31" />
<hkern u1="V" u2="m" k="31" />
<hkern u1="V" u2="g" k="27" />
<hkern u1="V" u2="e" k="27" />
<hkern u1="V" u2="d" k="27" />
<hkern u1="V" u2="c" k="27" />
<hkern u1="V" u2="a" k="27" />
<hkern u1="V" u2="Q" k="31" />
<hkern u1="V" u2="O" k="31" />
<hkern u1="V" u2="G" k="31" />
<hkern u1="V" u2="C" k="31" />
<hkern u1="V" u2="A" k="27" />
<hkern u1="V" u2="&#x3f;" k="-31" />
<hkern u1="V" u2="&#x2e;" k="57" />
<hkern u1="V" u2="&#x2c;" k="57" />
<hkern u1="V" u2="&#x27;" k="-61" />
<hkern u1="V" u2="&#x22;" k="-61" />
<hkern u1="W" u2="&#x201d;" k="-74" />
<hkern u1="W" u2="&#x2019;" k="-74" />
<hkern u1="W" u2="&#x153;" k="31" />
<hkern u1="W" u2="&#xf8;" k="31" />
<hkern u1="W" u2="&#xf6;" k="31" />
<hkern u1="W" u2="&#xf5;" k="31" />
<hkern u1="W" u2="&#xf4;" k="31" />
<hkern u1="W" u2="&#xf3;" k="31" />
<hkern u1="W" u2="&#xf2;" k="31" />
<hkern u1="W" u2="&#xeb;" k="31" />
<hkern u1="W" u2="&#xea;" k="31" />
<hkern u1="W" u2="&#xe9;" k="31" />
<hkern u1="W" u2="&#xe8;" k="31" />
<hkern u1="W" u2="&#xe7;" k="31" />
<hkern u1="W" u2="&#xe6;" k="31" />
<hkern u1="W" u2="&#xe5;" k="31" />
<hkern u1="W" u2="&#xe4;" k="31" />
<hkern u1="W" u2="&#xe3;" k="31" />
<hkern u1="W" u2="&#xe2;" k="31" />
<hkern u1="W" u2="&#xe1;" k="31" />
<hkern u1="W" u2="&#xe0;" k="31" />
<hkern u1="W" u2="&#xc6;" k="39" />
<hkern u1="W" u2="&#xc5;" k="31" />
<hkern u1="W" u2="&#xc4;" k="31" />
<hkern u1="W" u2="&#xc3;" k="31" />
<hkern u1="W" u2="&#xc2;" k="31" />
<hkern u1="W" u2="&#xc1;" k="31" />
<hkern u1="W" u2="&#xc0;" k="31" />
<hkern u1="W" u2="s" k="31" />
<hkern u1="W" u2="q" k="31" />
<hkern u1="W" u2="o" k="31" />
<hkern u1="W" u2="e" k="31" />
<hkern u1="W" u2="d" k="31" />
<hkern u1="W" u2="c" k="31" />
<hkern u1="W" u2="a" k="31" />
<hkern u1="W" u2="A" k="31" />
<hkern u1="W" u2="&#x2e;" k="41" />
<hkern u1="W" u2="&#x2c;" k="41" />
<hkern u1="W" u2="&#x27;" k="-76" />
<hkern u1="W" u2="&#x22;" k="-76" />
<hkern u1="X" u2="&#x201d;" k="-31" />
<hkern u1="X" u2="&#x2019;" k="-31" />
<hkern u1="X" u2="&#x152;" k="31" />
<hkern u1="X" u2="&#xd8;" k="31" />
<hkern u1="X" u2="&#xd6;" k="31" />
<hkern u1="X" u2="&#xd5;" k="31" />
<hkern u1="X" u2="&#xd4;" k="31" />
<hkern u1="X" u2="&#xd3;" k="31" />
<hkern u1="X" u2="&#xd2;" k="31" />
<hkern u1="X" u2="&#xc7;" k="31" />
<hkern u1="X" u2="Q" k="31" />
<hkern u1="X" u2="O" k="31" />
<hkern u1="X" u2="G" k="31" />
<hkern u1="X" u2="C" k="31" />
<hkern u1="X" u2="&#x27;" k="-31" />
<hkern u1="X" u2="&#x22;" k="-31" />
<hkern u1="Y" u2="&#x201d;" k="-59" />
<hkern u1="Y" u2="&#x2019;" k="-59" />
<hkern u1="Y" u2="&#x153;" k="55" />
<hkern u1="Y" u2="&#x152;" k="31" />
<hkern u1="Y" u2="&#xfc;" k="27" />
<hkern u1="Y" u2="&#xfb;" k="27" />
<hkern u1="Y" u2="&#xfa;" k="27" />
<hkern u1="Y" u2="&#xf9;" k="25" />
<hkern u1="Y" u2="&#xf8;" k="55" />
<hkern u1="Y" u2="&#xf6;" k="55" />
<hkern u1="Y" u2="&#xf5;" k="55" />
<hkern u1="Y" u2="&#xf4;" k="55" />
<hkern u1="Y" u2="&#xf3;" k="55" />
<hkern u1="Y" u2="&#xf2;" k="57" />
<hkern u1="Y" u2="&#xf1;" k="25" />
<hkern u1="Y" u2="&#xeb;" k="55" />
<hkern u1="Y" u2="&#xea;" k="55" />
<hkern u1="Y" u2="&#xe9;" k="55" />
<hkern u1="Y" u2="&#xe8;" k="55" />
<hkern u1="Y" u2="&#xe7;" k="55" />
<hkern u1="Y" u2="&#xe6;" k="57" />
<hkern u1="Y" u2="&#xe5;" k="57" />
<hkern u1="Y" u2="&#xe4;" k="57" />
<hkern u1="Y" u2="&#xe3;" k="57" />
<hkern u1="Y" u2="&#xe2;" k="57" />
<hkern u1="Y" u2="&#xe1;" k="57" />
<hkern u1="Y" u2="&#xe0;" k="57" />
<hkern u1="Y" u2="&#xd8;" k="31" />
<hkern u1="Y" u2="&#xd6;" k="31" />
<hkern u1="Y" u2="&#xd5;" k="31" />
<hkern u1="Y" u2="&#xd4;" k="31" />
<hkern u1="Y" u2="&#xd3;" k="31" />
<hkern u1="Y" u2="&#xd2;" k="31" />
<hkern u1="Y" u2="&#xc7;" k="31" />
<hkern u1="Y" u2="&#xc6;" k="84" />
<hkern u1="Y" u2="&#xc5;" k="35" />
<hkern u1="Y" u2="&#xc4;" k="55" />
<hkern u1="Y" u2="&#xc3;" k="57" />
<hkern u1="Y" u2="&#xc2;" k="57" />
<hkern u1="Y" u2="&#xc1;" k="57" />
<hkern u1="Y" u2="&#xc0;" k="57" />
<hkern u1="Y" u2="z" k="27" />
<hkern u1="Y" u2="x" k="31" />
<hkern u1="Y" u2="u" k="27" />
<hkern u1="Y" u2="s" k="41" />
<hkern u1="Y" u2="r" k="27" />
<hkern u1="Y" u2="q" k="57" />
<hkern u1="Y" u2="p" k="27" />
<hkern u1="Y" u2="o" k="57" />
<hkern u1="Y" u2="n" k="27" />
<hkern u1="Y" u2="m" k="27" />
<hkern u1="Y" u2="g" k="57" />
<hkern u1="Y" u2="e" k="57" />
<hkern u1="Y" u2="d" k="57" />
<hkern u1="Y" u2="c" k="57" />
<hkern u1="Y" u2="a" k="57" />
<hkern u1="Y" u2="Q" k="31" />
<hkern u1="Y" u2="O" k="31" />
<hkern u1="Y" u2="G" k="31" />
<hkern u1="Y" u2="C" k="31" />
<hkern u1="Y" u2="A" k="57" />
<hkern u1="Y" u2="&#x3f;" k="-31" />
<hkern u1="Y" u2="&#x2e;" k="57" />
<hkern u1="Y" u2="&#x2c;" k="57" />
<hkern u1="Y" u2="&#x27;" k="-61" />
<hkern u1="Y" u2="&#x22;" k="-61" />
<hkern u1="Z" u2="&#x201d;" k="-31" />
<hkern u1="Z" u2="&#x2019;" k="-31" />
<hkern u1="Z" u2="&#x152;" k="31" />
<hkern u1="Z" u2="&#x27;" k="-31" />
<hkern u1="Z" u2="&#x22;" k="-31" />
<hkern u1="[" u2="J" k="-123" />
<hkern u1="a" u2="&#x27;" k="31" />
<hkern u1="b" u2="&#x27;" k="31" />
<hkern u1="c" u2="&#x201d;" k="-47" />
<hkern u1="c" u2="&#x2019;" k="-47" />
<hkern u1="c" u2="&#x27;" k="-49" />
<hkern u1="c" u2="&#x22;" k="-49" />
<hkern u1="e" u2="&#x27;" k="31" />
<hkern u1="f" u2="&#x201d;" k="-74" />
<hkern u1="f" u2="&#x2019;" k="-74" />
<hkern u1="f" u2="&#x27;" k="-76" />
<hkern u1="f" u2="&#x22;" k="-76" />
<hkern u1="g" u2="&#x201d;" k="-31" />
<hkern u1="g" u2="&#x2019;" k="-31" />
<hkern u1="g" u2="&#x27;" k="-31" />
<hkern u1="g" u2="&#x22;" k="-31" />
<hkern u1="h" u2="&#x27;" k="27" />
<hkern u1="k" u2="&#x201d;" k="-33" />
<hkern u1="k" u2="&#x2019;" k="-33" />
<hkern u1="k" u2="&#x27;" k="-33" />
<hkern u1="k" u2="&#x22;" k="-33" />
<hkern u1="m" u2="&#x27;" k="20" />
<hkern u1="o" u2="x" k="31" />
<hkern u1="r" u2="&#x201d;" k="-74" />
<hkern u1="r" u2="&#x2019;" k="-74" />
<hkern u1="r" u2="&#x27;" k="-76" />
<hkern u1="r" u2="&#x22;" k="-76" />
<hkern u1="s" u2="&#x201d;" k="-43" />
<hkern u1="s" u2="&#x2019;" k="-43" />
<hkern u1="s" u2="&#x27;" k="-45" />
<hkern u1="s" u2="&#x22;" k="-45" />
<hkern u1="t" u2="&#x201d;" k="-59" />
<hkern u1="t" u2="&#x2019;" k="-59" />
<hkern u1="t" u2="&#x27;" k="-61" />
<hkern u1="t" u2="&#x22;" k="-61" />
<hkern u1="v" u2="&#x201d;" k="-59" />
<hkern u1="v" u2="&#x2019;" k="-59" />
<hkern u1="v" u2="&#x27;" k="-61" />
<hkern u1="v" u2="&#x22;" k="-61" />
<hkern u1="w" u2="&#x201d;" k="-63" />
<hkern u1="w" u2="&#x2019;" k="-63" />
<hkern u1="w" u2="&#x27;" k="-66" />
<hkern u1="w" u2="&#x22;" k="-66" />
<hkern u1="x" u2="&#x201d;" k="-33" />
<hkern u1="x" u2="&#x2019;" k="-33" />
<hkern u1="x" u2="&#x153;" k="10" />
<hkern u1="x" u2="&#xf8;" k="10" />
<hkern u1="x" u2="&#xf6;" k="10" />
<hkern u1="x" u2="&#xf5;" k="10" />
<hkern u1="x" u2="&#xf4;" k="10" />
<hkern u1="x" u2="&#xf3;" k="10" />
<hkern u1="x" u2="&#xf2;" k="12" />
<hkern u1="x" u2="&#xe6;" k="10" />
<hkern u1="x" u2="o" k="12" />
<hkern u1="x" u2="&#x27;" k="-33" />
<hkern u1="x" u2="&#x22;" k="-33" />
<hkern u1="y" u2="&#x201d;" k="-43" />
<hkern u1="y" u2="&#x2019;" k="-43" />
<hkern u1="y" u2="&#x27;" k="-45" />
<hkern u1="y" u2="&#x22;" k="-45" />
<hkern u1="&#x7b;" u2="J" k="-123" />
<hkern u1="&#xc0;" u2="&#x201d;" k="59" />
<hkern u1="&#xc0;" u2="&#x2019;" k="59" />
<hkern u1="&#xc0;" u2="&#x178;" k="80" />
<hkern u1="&#xc0;" u2="&#x152;" k="20" />
<hkern u1="&#xc0;" u2="&#xff;" k="18" />
<hkern u1="&#xc0;" u2="&#xfd;" k="18" />
<hkern u1="&#xc0;" u2="&#xdd;" k="80" />
<hkern u1="&#xc0;" u2="&#xc0;" k="18" />
<hkern u1="&#xc0;" u2="y" k="20" />
<hkern u1="&#xc0;" u2="v" k="20" />
<hkern u1="&#xc0;" u2="Z" k="-59" />
<hkern u1="&#xc0;" u2="Y" k="82" />
<hkern u1="&#xc0;" u2="W" k="20" />
<hkern u1="&#xc0;" u2="V" k="41" />
<hkern u1="&#xc0;" u2="T" k="102" />
<hkern u1="&#xc0;" u2="J" k="-94" />
<hkern u1="&#xc0;" u2="&#x3f;" k="41" />
<hkern u1="&#xc0;" u2="&#x3b;" k="-68" />
<hkern u1="&#xc0;" u2="&#x2c;" k="-68" />
<hkern u1="&#xc0;" u2="&#x2a;" k="102" />
<hkern u1="&#xc0;" u2="&#x27;" k="61" />
<hkern u1="&#xc0;" u2="&#x22;" k="61" />
<hkern u1="&#xc1;" u2="&#x201d;" k="59" />
<hkern u1="&#xc1;" u2="&#x2019;" k="59" />
<hkern u1="&#xc1;" u2="&#x178;" k="80" />
<hkern u1="&#xc1;" u2="&#x152;" k="20" />
<hkern u1="&#xc1;" u2="&#xff;" k="18" />
<hkern u1="&#xc1;" u2="&#xfd;" k="18" />
<hkern u1="&#xc1;" u2="&#xdd;" k="80" />
<hkern u1="&#xc1;" u2="&#xcd;" k="20" />
<hkern u1="&#xc1;" u2="&#xc8;" k="20" />
<hkern u1="&#xc1;" u2="&#xc2;" k="20" />
<hkern u1="&#xc1;" u2="y" k="20" />
<hkern u1="&#xc1;" u2="v" k="20" />
<hkern u1="&#xc1;" u2="Z" k="-59" />
<hkern u1="&#xc1;" u2="Y" k="82" />
<hkern u1="&#xc1;" u2="W" k="20" />
<hkern u1="&#xc1;" u2="V" k="41" />
<hkern u1="&#xc1;" u2="T" k="102" />
<hkern u1="&#xc1;" u2="J" k="-96" />
<hkern u1="&#xc1;" u2="&#x3f;" k="41" />
<hkern u1="&#xc1;" u2="&#x3b;" k="-68" />
<hkern u1="&#xc1;" u2="&#x2c;" k="-68" />
<hkern u1="&#xc1;" u2="&#x2a;" k="102" />
<hkern u1="&#xc1;" u2="&#x27;" k="61" />
<hkern u1="&#xc1;" u2="&#x22;" k="61" />
<hkern u1="&#xc2;" u2="&#x201d;" k="59" />
<hkern u1="&#xc2;" u2="&#x2019;" k="59" />
<hkern u1="&#xc2;" u2="&#x178;" k="80" />
<hkern u1="&#xc2;" u2="&#x152;" k="20" />
<hkern u1="&#xc2;" u2="&#xff;" k="18" />
<hkern u1="&#xc2;" u2="&#xfd;" k="18" />
<hkern u1="&#xc2;" u2="&#xdd;" k="80" />
<hkern u1="&#xc2;" u2="&#xcd;" k="20" />
<hkern u1="&#xc2;" u2="&#xc8;" k="20" />
<hkern u1="&#xc2;" u2="&#xc2;" k="20" />
<hkern u1="&#xc2;" u2="y" k="20" />
<hkern u1="&#xc2;" u2="v" k="20" />
<hkern u1="&#xc2;" u2="Z" k="-59" />
<hkern u1="&#xc2;" u2="Y" k="82" />
<hkern u1="&#xc2;" u2="W" k="20" />
<hkern u1="&#xc2;" u2="V" k="41" />
<hkern u1="&#xc2;" u2="T" k="102" />
<hkern u1="&#xc2;" u2="J" k="-96" />
<hkern u1="&#xc2;" u2="&#x3f;" k="41" />
<hkern u1="&#xc2;" u2="&#x3b;" k="-68" />
<hkern u1="&#xc2;" u2="&#x2c;" k="-68" />
<hkern u1="&#xc2;" u2="&#x2a;" k="102" />
<hkern u1="&#xc2;" u2="&#x27;" k="61" />
<hkern u1="&#xc2;" u2="&#x22;" k="61" />
<hkern u1="&#xc3;" u2="&#x201d;" k="59" />
<hkern u1="&#xc3;" u2="&#x2019;" k="59" />
<hkern u1="&#xc3;" u2="&#x178;" k="80" />
<hkern u1="&#xc3;" u2="&#x152;" k="20" />
<hkern u1="&#xc3;" u2="&#xff;" k="18" />
<hkern u1="&#xc3;" u2="&#xfd;" k="18" />
<hkern u1="&#xc3;" u2="&#xdd;" k="80" />
<hkern u1="&#xc3;" u2="&#xc0;" k="18" />
<hkern u1="&#xc3;" u2="y" k="20" />
<hkern u1="&#xc3;" u2="v" k="20" />
<hkern u1="&#xc3;" u2="Z" k="-59" />
<hkern u1="&#xc3;" u2="Y" k="82" />
<hkern u1="&#xc3;" u2="W" k="20" />
<hkern u1="&#xc3;" u2="V" k="41" />
<hkern u1="&#xc3;" u2="T" k="102" />
<hkern u1="&#xc3;" u2="J" k="-94" />
<hkern u1="&#xc3;" u2="&#x3f;" k="41" />
<hkern u1="&#xc3;" u2="&#x3b;" k="-68" />
<hkern u1="&#xc3;" u2="&#x2c;" k="-68" />
<hkern u1="&#xc3;" u2="&#x2a;" k="102" />
<hkern u1="&#xc3;" u2="&#x27;" k="61" />
<hkern u1="&#xc3;" u2="&#x22;" k="61" />
<hkern u1="&#xc4;" u2="&#x201d;" k="59" />
<hkern u1="&#xc4;" u2="&#x2019;" k="59" />
<hkern u1="&#xc4;" u2="&#x178;" k="80" />
<hkern u1="&#xc4;" u2="&#x152;" k="20" />
<hkern u1="&#xc4;" u2="&#xff;" k="16" />
<hkern u1="&#xc4;" u2="&#xfd;" k="16" />
<hkern u1="&#xc4;" u2="&#xdd;" k="80" />
<hkern u1="&#xc4;" u2="y" k="16" />
<hkern u1="&#xc4;" u2="v" k="23" />
<hkern u1="&#xc4;" u2="Z" k="-59" />
<hkern u1="&#xc4;" u2="Y" k="82" />
<hkern u1="&#xc4;" u2="W" k="20" />
<hkern u1="&#xc4;" u2="V" k="41" />
<hkern u1="&#xc4;" u2="T" k="102" />
<hkern u1="&#xc4;" u2="J" k="-94" />
<hkern u1="&#xc4;" u2="&#x3f;" k="20" />
<hkern u1="&#xc4;" u2="&#x3b;" k="-68" />
<hkern u1="&#xc4;" u2="&#x2c;" k="-68" />
<hkern u1="&#xc4;" u2="&#x2a;" k="109" />
<hkern u1="&#xc4;" u2="&#x27;" k="61" />
<hkern u1="&#xc4;" u2="&#x22;" k="61" />
<hkern u1="&#xc5;" u2="&#x2122;" k="80" />
<hkern u1="&#xc5;" u2="&#x201d;" k="59" />
<hkern u1="&#xc5;" u2="&#x201c;" k="131" />
<hkern u1="&#xc5;" u2="&#x2019;" k="59" />
<hkern u1="&#xc5;" u2="&#x2018;" k="133" />
<hkern u1="&#xc5;" u2="&#x178;" k="80" />
<hkern u1="&#xc5;" u2="&#x152;" k="20" />
<hkern u1="&#xc5;" u2="&#xff;" k="16" />
<hkern u1="&#xc5;" u2="&#xfd;" k="16" />
<hkern u1="&#xc5;" u2="&#xdd;" k="80" />
<hkern u1="&#xc5;" u2="y" k="16" />
<hkern u1="&#xc5;" u2="v" k="23" />
<hkern u1="&#xc5;" u2="Z" k="-59" />
<hkern u1="&#xc5;" u2="Y" k="82" />
<hkern u1="&#xc5;" u2="W" k="20" />
<hkern u1="&#xc5;" u2="V" k="20" />
<hkern u1="&#xc5;" u2="T" k="102" />
<hkern u1="&#xc5;" u2="J" k="-94" />
<hkern u1="&#xc5;" u2="&#x3f;" k="20" />
<hkern u1="&#xc5;" u2="&#x3b;" k="-68" />
<hkern u1="&#xc5;" u2="&#x2c;" k="-68" />
<hkern u1="&#xc5;" u2="&#x2a;" k="109" />
<hkern u1="&#xc5;" u2="&#x27;" k="133" />
<hkern u1="&#xc5;" u2="&#x22;" k="133" />
<hkern u1="&#xc7;" u2="&#x201d;" k="-41" />
<hkern u1="&#xc7;" u2="&#x2019;" k="-41" />
<hkern u1="&#xc7;" u2="&#xd8;" k="18" />
<hkern u1="&#xc7;" u2="&#xd6;" k="18" />
<hkern u1="&#xc7;" u2="&#xd5;" k="18" />
<hkern u1="&#xc7;" u2="&#xd4;" k="18" />
<hkern u1="&#xc7;" u2="&#xd3;" k="20" />
<hkern u1="&#xc7;" u2="&#xd2;" k="18" />
<hkern u1="&#xc7;" u2="&#xc7;" k="18" />
<hkern u1="&#xc7;" u2="&#x7d;" k="-41" />
<hkern u1="&#xc7;" u2="]" k="-41" />
<hkern u1="&#xc7;" u2="Q" k="20" />
<hkern u1="&#xc7;" u2="O" k="20" />
<hkern u1="&#xc7;" u2="G" k="20" />
<hkern u1="&#xc7;" u2="C" k="20" />
<hkern u1="&#xc7;" u2="&#x29;" k="-41" />
<hkern u1="&#xc7;" u2="&#x27;" k="-41" />
<hkern u1="&#xc7;" u2="&#x22;" k="-41" />
<hkern u1="&#xc8;" u2="&#x201d;" k="-41" />
<hkern u1="&#xc8;" u2="&#x2019;" k="-41" />
<hkern u1="&#xc8;" u2="&#x2d;" k="20" />
<hkern u1="&#xc8;" u2="&#x27;" k="-41" />
<hkern u1="&#xc8;" u2="&#x22;" k="-41" />
<hkern u1="&#xc9;" u2="&#x201d;" k="-41" />
<hkern u1="&#xc9;" u2="&#x2019;" k="-41" />
<hkern u1="&#xc9;" u2="&#x2d;" k="20" />
<hkern u1="&#xc9;" u2="&#x27;" k="-41" />
<hkern u1="&#xc9;" u2="&#x22;" k="-41" />
<hkern u1="&#xca;" u2="&#x201d;" k="-41" />
<hkern u1="&#xca;" u2="&#x2019;" k="-41" />
<hkern u1="&#xca;" u2="&#x2d;" k="20" />
<hkern u1="&#xca;" u2="&#x27;" k="-41" />
<hkern u1="&#xca;" u2="&#x22;" k="-41" />
<hkern u1="&#xcb;" u2="&#x201d;" k="-41" />
<hkern u1="&#xcb;" u2="&#x2019;" k="-41" />
<hkern u1="&#xcb;" u2="&#x2d;" k="20" />
<hkern u1="&#xcb;" u2="&#x27;" k="-41" />
<hkern u1="&#xcb;" u2="&#x22;" k="-41" />
<hkern u1="&#xcc;" u2="&#x201d;" k="-41" />
<hkern u1="&#xcc;" u2="&#x2019;" k="-41" />
<hkern u1="&#xcc;" u2="&#x27;" k="-41" />
<hkern u1="&#xcc;" u2="&#x22;" k="-41" />
<hkern u1="&#xcd;" u2="&#x201d;" k="-41" />
<hkern u1="&#xcd;" u2="&#x2019;" k="-41" />
<hkern u1="&#xcd;" u2="&#x27;" k="-41" />
<hkern u1="&#xcd;" u2="&#x22;" k="-41" />
<hkern u1="&#xce;" u2="&#x201d;" k="-41" />
<hkern u1="&#xce;" u2="&#x2019;" k="-41" />
<hkern u1="&#xce;" u2="&#x27;" k="-41" />
<hkern u1="&#xce;" u2="&#x22;" k="-41" />
<hkern u1="&#xcf;" u2="&#x201d;" k="-41" />
<hkern u1="&#xcf;" u2="&#x2019;" k="-41" />
<hkern u1="&#xcf;" u2="&#x27;" k="-41" />
<hkern u1="&#xcf;" u2="&#x22;" k="-41" />
<hkern u1="&#xd2;" u2="&#x178;" k="18" />
<hkern u1="&#xd2;" u2="&#xdd;" k="18" />
<hkern u1="&#xd2;" u2="&#xc6;" k="18" />
<hkern u1="&#xd2;" u2="&#x7d;" k="20" />
<hkern u1="&#xd2;" u2="]" k="20" />
<hkern u1="&#xd2;" u2="Y" k="20" />
<hkern u1="&#xd2;" u2="X" k="20" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd2;" u2="T" k="41" />
<hkern u1="&#xd2;" u2="&#x2e;" k="41" />
<hkern u1="&#xd2;" u2="&#x2c;" k="41" />
<hkern u1="&#xd2;" u2="&#x29;" k="20" />
<hkern u1="&#xd3;" u2="&#x178;" k="18" />
<hkern u1="&#xd3;" u2="&#xdd;" k="18" />
<hkern u1="&#xd3;" u2="&#xc6;" k="18" />
<hkern u1="&#xd3;" u2="&#x7d;" k="20" />
<hkern u1="&#xd3;" u2="]" k="20" />
<hkern u1="&#xd3;" u2="Y" k="20" />
<hkern u1="&#xd3;" u2="X" k="20" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="T" k="41" />
<hkern u1="&#xd3;" u2="&#x2e;" k="41" />
<hkern u1="&#xd3;" u2="&#x2c;" k="41" />
<hkern u1="&#xd3;" u2="&#x29;" k="20" />
<hkern u1="&#xd4;" u2="&#x178;" k="18" />
<hkern u1="&#xd4;" u2="&#xdd;" k="18" />
<hkern u1="&#xd4;" u2="&#xc6;" k="18" />
<hkern u1="&#xd4;" u2="&#x7d;" k="20" />
<hkern u1="&#xd4;" u2="]" k="20" />
<hkern u1="&#xd4;" u2="Y" k="20" />
<hkern u1="&#xd4;" u2="X" k="20" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="T" k="41" />
<hkern u1="&#xd4;" u2="&#x2e;" k="41" />
<hkern u1="&#xd4;" u2="&#x2c;" k="41" />
<hkern u1="&#xd4;" u2="&#x29;" k="20" />
<hkern u1="&#xd5;" u2="&#x178;" k="18" />
<hkern u1="&#xd5;" u2="&#xdd;" k="18" />
<hkern u1="&#xd5;" u2="&#xc6;" k="18" />
<hkern u1="&#xd5;" u2="&#x7d;" k="20" />
<hkern u1="&#xd5;" u2="]" k="20" />
<hkern u1="&#xd5;" u2="Y" k="20" />
<hkern u1="&#xd5;" u2="X" k="20" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="T" k="41" />
<hkern u1="&#xd5;" u2="&#x2e;" k="41" />
<hkern u1="&#xd5;" u2="&#x2c;" k="41" />
<hkern u1="&#xd5;" u2="&#x29;" k="20" />
<hkern u1="&#xd6;" u2="&#x178;" k="18" />
<hkern u1="&#xd6;" u2="&#xdd;" k="18" />
<hkern u1="&#xd6;" u2="&#xc6;" k="18" />
<hkern u1="&#xd6;" u2="&#x7d;" k="20" />
<hkern u1="&#xd6;" u2="]" k="20" />
<hkern u1="&#xd6;" u2="Y" k="20" />
<hkern u1="&#xd6;" u2="X" k="20" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="T" k="41" />
<hkern u1="&#xd6;" u2="&#x2e;" k="41" />
<hkern u1="&#xd6;" u2="&#x2c;" k="41" />
<hkern u1="&#xd6;" u2="&#x29;" k="20" />
<hkern u1="&#xd8;" u2="&#x178;" k="18" />
<hkern u1="&#xd8;" u2="&#xdd;" k="18" />
<hkern u1="&#xd8;" u2="&#xc6;" k="18" />
<hkern u1="&#xd8;" u2="Y" k="20" />
<hkern u1="&#xd8;" u2="X" k="20" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="T" k="41" />
<hkern u1="&#xd8;" u2="&#x2e;" k="41" />
<hkern u1="&#xd8;" u2="&#x2c;" k="41" />
<hkern u1="&#xd9;" u2="&#xc6;" k="18" />
<hkern u1="&#xd9;" u2="&#x2e;" k="20" />
<hkern u1="&#xd9;" u2="&#x2c;" k="20" />
<hkern u1="&#xda;" u2="&#xc6;" k="18" />
<hkern u1="&#xda;" u2="&#x2e;" k="20" />
<hkern u1="&#xda;" u2="&#x2c;" k="20" />
<hkern u1="&#xdb;" u2="&#xc6;" k="18" />
<hkern u1="&#xdb;" u2="&#x2e;" k="20" />
<hkern u1="&#xdb;" u2="&#x2c;" k="20" />
<hkern u1="&#xdc;" u2="&#xc6;" k="18" />
<hkern u1="&#xdc;" u2="&#x2e;" k="20" />
<hkern u1="&#xdc;" u2="&#x2c;" k="20" />
<hkern u1="&#xdd;" u2="&#x201d;" k="-80" />
<hkern u1="&#xdd;" u2="&#x2019;" k="-80" />
<hkern u1="&#xdd;" u2="&#x153;" k="80" />
<hkern u1="&#xdd;" u2="&#x152;" k="18" />
<hkern u1="&#xdd;" u2="&#xfc;" k="41" />
<hkern u1="&#xdd;" u2="&#xfb;" k="41" />
<hkern u1="&#xdd;" u2="&#xfa;" k="41" />
<hkern u1="&#xdd;" u2="&#xf9;" k="41" />
<hkern u1="&#xdd;" u2="&#xf8;" k="80" />
<hkern u1="&#xdd;" u2="&#xf6;" k="82" />
<hkern u1="&#xdd;" u2="&#xf5;" k="82" />
<hkern u1="&#xdd;" u2="&#xf4;" k="82" />
<hkern u1="&#xdd;" u2="&#xf3;" k="82" />
<hkern u1="&#xdd;" u2="&#xf2;" k="82" />
<hkern u1="&#xdd;" u2="&#xf1;" k="39" />
<hkern u1="&#xdd;" u2="&#xeb;" k="80" />
<hkern u1="&#xdd;" u2="&#xea;" k="80" />
<hkern u1="&#xdd;" u2="&#xe9;" k="80" />
<hkern u1="&#xdd;" u2="&#xe8;" k="80" />
<hkern u1="&#xdd;" u2="&#xe7;" k="80" />
<hkern u1="&#xdd;" u2="&#xe6;" k="82" />
<hkern u1="&#xdd;" u2="&#xe5;" k="82" />
<hkern u1="&#xdd;" u2="&#xe4;" k="82" />
<hkern u1="&#xdd;" u2="&#xe3;" k="82" />
<hkern u1="&#xdd;" u2="&#xe2;" k="82" />
<hkern u1="&#xdd;" u2="&#xe1;" k="82" />
<hkern u1="&#xdd;" u2="&#xe0;" k="82" />
<hkern u1="&#xdd;" u2="&#xd8;" k="18" />
<hkern u1="&#xdd;" u2="&#xd6;" k="18" />
<hkern u1="&#xdd;" u2="&#xd5;" k="18" />
<hkern u1="&#xdd;" u2="&#xd4;" k="18" />
<hkern u1="&#xdd;" u2="&#xd3;" k="20" />
<hkern u1="&#xdd;" u2="&#xd2;" k="18" />
<hkern u1="&#xdd;" u2="&#xc7;" k="18" />
<hkern u1="&#xdd;" u2="&#xc6;" k="121" />
<hkern u1="&#xdd;" u2="&#xc5;" k="80" />
<hkern u1="&#xdd;" u2="&#xc4;" k="80" />
<hkern u1="&#xdd;" u2="&#xc3;" k="82" />
<hkern u1="&#xdd;" u2="&#xc2;" k="82" />
<hkern u1="&#xdd;" u2="&#xc1;" k="82" />
<hkern u1="&#xdd;" u2="&#xc0;" k="82" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-61" />
<hkern u1="&#xdd;" u2="z" k="41" />
<hkern u1="&#xdd;" u2="x" k="20" />
<hkern u1="&#xdd;" u2="v" k="20" />
<hkern u1="&#xdd;" u2="u" k="41" />
<hkern u1="&#xdd;" u2="s" k="61" />
<hkern u1="&#xdd;" u2="r" k="41" />
<hkern u1="&#xdd;" u2="q" k="82" />
<hkern u1="&#xdd;" u2="p" k="41" />
<hkern u1="&#xdd;" u2="o" k="82" />
<hkern u1="&#xdd;" u2="n" k="41" />
<hkern u1="&#xdd;" u2="m" k="41" />
<hkern u1="&#xdd;" u2="g" k="82" />
<hkern u1="&#xdd;" u2="e" k="82" />
<hkern u1="&#xdd;" u2="d" k="82" />
<hkern u1="&#xdd;" u2="c" k="82" />
<hkern u1="&#xdd;" u2="a" k="82" />
<hkern u1="&#xdd;" u2="]" k="-61" />
<hkern u1="&#xdd;" u2="V" k="-41" />
<hkern u1="&#xdd;" u2="T" k="-39" />
<hkern u1="&#xdd;" u2="Q" k="20" />
<hkern u1="&#xdd;" u2="O" k="20" />
<hkern u1="&#xdd;" u2="M" k="41" />
<hkern u1="&#xdd;" u2="J" k="45" />
<hkern u1="&#xdd;" u2="G" k="20" />
<hkern u1="&#xdd;" u2="C" k="20" />
<hkern u1="&#xdd;" u2="A" k="82" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-41" />
<hkern u1="&#xdd;" u2="&#x2e;" k="82" />
<hkern u1="&#xdd;" u2="&#x2d;" k="82" />
<hkern u1="&#xdd;" u2="&#x2c;" k="82" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-41" />
<hkern u1="&#xdd;" u2="&#x29;" k="-61" />
<hkern u1="&#xdd;" u2="&#x27;" k="-82" />
<hkern u1="&#xdd;" u2="&#x26;" k="41" />
<hkern u1="&#xdd;" u2="&#x22;" k="-82" />
<hkern u1="&#xe0;" u2="&#x27;" k="20" />
<hkern u1="&#xe1;" u2="&#x27;" k="20" />
<hkern u1="&#xe2;" u2="&#x27;" k="20" />
<hkern u1="&#xe3;" u2="&#x27;" k="20" />
<hkern u1="&#xe4;" u2="&#x27;" k="20" />
<hkern u1="&#xe5;" u2="&#x27;" k="20" />
<hkern u1="&#xe6;" u2="x" k="31" />
<hkern u1="&#xe7;" u2="&#x201d;" k="-61" />
<hkern u1="&#xe7;" u2="&#x2019;" k="-61" />
<hkern u1="&#xe7;" u2="&#x27;" k="-61" />
<hkern u1="&#xe7;" u2="&#x22;" k="-61" />
<hkern u1="&#xe8;" u2="&#x27;" k="20" />
<hkern u1="&#xe8;" u2="&#x22;" k="84" />
<hkern u1="&#xe9;" u2="&#x27;" k="20" />
<hkern u1="&#xe9;" u2="&#x22;" k="84" />
<hkern u1="&#xea;" u2="&#x27;" k="20" />
<hkern u1="&#xea;" u2="&#x22;" k="84" />
<hkern u1="&#xeb;" u2="&#x27;" k="20" />
<hkern u1="&#xeb;" u2="&#x22;" k="84" />
<hkern u1="&#xf1;" u2="&#x201d;" k="18" />
<hkern u1="&#xf1;" u2="&#x27;" k="20" />
<hkern u1="&#xf1;" u2="&#x22;" k="84" />
<hkern u1="&#xf2;" u2="x" k="20" />
<hkern u1="&#xf2;" u2="f" k="16" />
<hkern u1="&#xf2;" u2="&#x27;" k="125" />
<hkern u1="&#xf2;" u2="&#x22;" k="125" />
<hkern u1="&#xf3;" u2="x" k="20" />
<hkern u1="&#xf3;" u2="f" k="16" />
<hkern u1="&#xf3;" u2="&#x27;" k="125" />
<hkern u1="&#xf3;" u2="&#x22;" k="125" />
<hkern u1="&#xf4;" u2="x" k="20" />
<hkern u1="&#xf4;" u2="f" k="16" />
<hkern u1="&#xf4;" u2="&#x27;" k="125" />
<hkern u1="&#xf4;" u2="&#x22;" k="125" />
<hkern u1="&#xf5;" u2="x" k="20" />
<hkern u1="&#xf5;" u2="f" k="16" />
<hkern u1="&#xf5;" u2="&#x27;" k="125" />
<hkern u1="&#xf5;" u2="&#x22;" k="125" />
<hkern u1="&#xf6;" u2="x" k="20" />
<hkern u1="&#xf6;" u2="f" k="16" />
<hkern u1="&#xf6;" u2="&#x27;" k="125" />
<hkern u1="&#xf6;" u2="&#x22;" k="125" />
<hkern u1="&#xf8;" u2="x" k="20" />
<hkern u1="&#xf9;" u2="&#x27;" k="45" />
<hkern u1="&#xf9;" u2="&#x22;" k="45" />
<hkern u1="&#xfa;" u2="&#x27;" k="45" />
<hkern u1="&#xfa;" u2="&#x22;" k="45" />
<hkern u1="&#xfb;" u2="&#x27;" k="45" />
<hkern u1="&#xfb;" u2="&#x22;" k="45" />
<hkern u1="&#xfc;" u2="&#x27;" k="45" />
<hkern u1="&#xfc;" u2="&#x22;" k="45" />
<hkern u1="&#xfd;" u2="&#x201d;" k="-61" />
<hkern u1="&#xfd;" u2="&#x2019;" k="-61" />
<hkern u1="&#xfd;" u2="&#x3f;" k="55" />
<hkern u1="&#xfd;" u2="&#x2e;" k="45" />
<hkern u1="&#xfd;" u2="&#x2c;" k="45" />
<hkern u1="&#xfd;" u2="&#x27;" k="-61" />
<hkern u1="&#xfd;" u2="&#x22;" k="-61" />
<hkern u1="&#xff;" u2="&#x201d;" k="-61" />
<hkern u1="&#xff;" u2="&#x2019;" k="-61" />
<hkern u1="&#xff;" u2="&#x2e;" k="45" />
<hkern u1="&#xff;" u2="&#x2c;" k="45" />
<hkern u1="&#xff;" u2="&#x27;" k="-61" />
<hkern u1="&#xff;" u2="&#x22;" k="-61" />
<hkern u1="&#x153;" u2="x" k="31" />
<hkern u1="&#x178;" u2="&#x2022;" k="18" />
<hkern u1="&#x178;" u2="&#x201d;" k="-80" />
<hkern u1="&#x178;" u2="&#x2019;" k="-80" />
<hkern u1="&#x178;" u2="&#x153;" k="80" />
<hkern u1="&#x178;" u2="&#x152;" k="18" />
<hkern u1="&#x178;" u2="&#xfc;" k="41" />
<hkern u1="&#x178;" u2="&#xfb;" k="41" />
<hkern u1="&#x178;" u2="&#xfa;" k="41" />
<hkern u1="&#x178;" u2="&#xf9;" k="41" />
<hkern u1="&#x178;" u2="&#xf8;" k="80" />
<hkern u1="&#x178;" u2="&#xf6;" k="82" />
<hkern u1="&#x178;" u2="&#xf5;" k="82" />
<hkern u1="&#x178;" u2="&#xf4;" k="82" />
<hkern u1="&#x178;" u2="&#xf3;" k="82" />
<hkern u1="&#x178;" u2="&#xf2;" k="82" />
<hkern u1="&#x178;" u2="&#xf1;" k="39" />
<hkern u1="&#x178;" u2="&#xeb;" k="80" />
<hkern u1="&#x178;" u2="&#xea;" k="80" />
<hkern u1="&#x178;" u2="&#xe9;" k="80" />
<hkern u1="&#x178;" u2="&#xe8;" k="80" />
<hkern u1="&#x178;" u2="&#xe7;" k="80" />
<hkern u1="&#x178;" u2="&#xe6;" k="82" />
<hkern u1="&#x178;" u2="&#xe5;" k="82" />
<hkern u1="&#x178;" u2="&#xe4;" k="82" />
<hkern u1="&#x178;" u2="&#xe3;" k="82" />
<hkern u1="&#x178;" u2="&#xe2;" k="82" />
<hkern u1="&#x178;" u2="&#xe1;" k="82" />
<hkern u1="&#x178;" u2="&#xe0;" k="82" />
<hkern u1="&#x178;" u2="&#xd8;" k="18" />
<hkern u1="&#x178;" u2="&#xd6;" k="18" />
<hkern u1="&#x178;" u2="&#xd5;" k="18" />
<hkern u1="&#x178;" u2="&#xd4;" k="18" />
<hkern u1="&#x178;" u2="&#xd3;" k="20" />
<hkern u1="&#x178;" u2="&#xd2;" k="18" />
<hkern u1="&#x178;" u2="&#xc7;" k="18" />
<hkern u1="&#x178;" u2="&#xc6;" k="121" />
<hkern u1="&#x178;" u2="&#xc5;" k="80" />
<hkern u1="&#x178;" u2="&#xc4;" k="80" />
<hkern u1="&#x178;" u2="&#xc3;" k="82" />
<hkern u1="&#x178;" u2="&#xc2;" k="82" />
<hkern u1="&#x178;" u2="&#xc1;" k="82" />
<hkern u1="&#x178;" u2="&#xc0;" k="82" />
<hkern u1="&#x178;" u2="&#x7d;" k="-61" />
<hkern u1="&#x178;" u2="z" k="41" />
<hkern u1="&#x178;" u2="x" k="20" />
<hkern u1="&#x178;" u2="u" k="41" />
<hkern u1="&#x178;" u2="s" k="61" />
<hkern u1="&#x178;" u2="r" k="41" />
<hkern u1="&#x178;" u2="q" k="82" />
<hkern u1="&#x178;" u2="p" k="41" />
<hkern u1="&#x178;" u2="o" k="82" />
<hkern u1="&#x178;" u2="n" k="41" />
<hkern u1="&#x178;" u2="m" k="41" />
<hkern u1="&#x178;" u2="g" k="82" />
<hkern u1="&#x178;" u2="e" k="82" />
<hkern u1="&#x178;" u2="d" k="82" />
<hkern u1="&#x178;" u2="c" k="82" />
<hkern u1="&#x178;" u2="a" k="82" />
<hkern u1="&#x178;" u2="]" k="-61" />
<hkern u1="&#x178;" u2="T" k="-39" />
<hkern u1="&#x178;" u2="Q" k="20" />
<hkern u1="&#x178;" u2="O" k="20" />
<hkern u1="&#x178;" u2="J" k="45" />
<hkern u1="&#x178;" u2="G" k="20" />
<hkern u1="&#x178;" u2="C" k="20" />
<hkern u1="&#x178;" u2="A" k="82" />
<hkern u1="&#x178;" u2="&#x3f;" k="-41" />
<hkern u1="&#x178;" u2="&#x2e;" k="82" />
<hkern u1="&#x178;" u2="&#x2d;" k="143" />
<hkern u1="&#x178;" u2="&#x2c;" k="82" />
<hkern u1="&#x178;" u2="&#x2a;" k="-41" />
<hkern u1="&#x178;" u2="&#x29;" k="-61" />
<hkern u1="&#x178;" u2="&#x27;" k="-82" />
<hkern u1="&#x178;" u2="&#x26;" k="41" />
<hkern u1="&#x178;" u2="&#x22;" k="-82" />
<hkern u1="&#x2013;" u2="T" k="57" />
<hkern u1="&#x2014;" u2="T" k="57" />
<hkern u1="&#x2018;" u2="&#x178;" k="-80" />
<hkern u1="&#x2018;" u2="&#x153;" k="39" />
<hkern u1="&#x2018;" u2="&#xf8;" k="39" />
<hkern u1="&#x2018;" u2="&#xf6;" k="39" />
<hkern u1="&#x2018;" u2="&#xf5;" k="39" />
<hkern u1="&#x2018;" u2="&#xf4;" k="39" />
<hkern u1="&#x2018;" u2="&#xf3;" k="39" />
<hkern u1="&#x2018;" u2="&#xf2;" k="41" />
<hkern u1="&#x2018;" u2="&#xeb;" k="39" />
<hkern u1="&#x2018;" u2="&#xea;" k="39" />
<hkern u1="&#x2018;" u2="&#xe9;" k="39" />
<hkern u1="&#x2018;" u2="&#xe8;" k="39" />
<hkern u1="&#x2018;" u2="&#xe7;" k="39" />
<hkern u1="&#x2018;" u2="&#xe6;" k="41" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-80" />
<hkern u1="&#x2018;" u2="&#xcf;" k="-41" />
<hkern u1="&#x2018;" u2="&#xce;" k="-41" />
<hkern u1="&#x2018;" u2="&#xcd;" k="-41" />
<hkern u1="&#x2018;" u2="&#xcc;" k="-41" />
<hkern u1="&#x2018;" u2="&#xc6;" k="141" />
<hkern u1="&#x2018;" u2="&#xc5;" k="59" />
<hkern u1="&#x2018;" u2="&#xc4;" k="59" />
<hkern u1="&#x2018;" u2="&#xc3;" k="61" />
<hkern u1="&#x2018;" u2="&#xc2;" k="61" />
<hkern u1="&#x2018;" u2="&#xc1;" k="61" />
<hkern u1="&#x2018;" u2="&#xc0;" k="61" />
<hkern u1="&#x2018;" u2="v" k="-41" />
<hkern u1="&#x2018;" u2="t" k="-41" />
<hkern u1="&#x2018;" u2="q" k="41" />
<hkern u1="&#x2018;" u2="o" k="41" />
<hkern u1="&#x2018;" u2="g" k="20" />
<hkern u1="&#x2018;" u2="e" k="41" />
<hkern u1="&#x2018;" u2="d" k="41" />
<hkern u1="&#x2018;" u2="c" k="41" />
<hkern u1="&#x2018;" u2="Z" k="-41" />
<hkern u1="&#x2018;" u2="Y" k="-82" />
<hkern u1="&#x2018;" u2="X" k="-41" />
<hkern u1="&#x2018;" u2="W" k="-102" />
<hkern u1="&#x2018;" u2="V" k="-82" />
<hkern u1="&#x2018;" u2="T" k="-82" />
<hkern u1="&#x2018;" u2="I" k="-41" />
<hkern u1="&#x2018;" u2="A" k="61" />
<hkern u1="&#x201c;" u2="&#x178;" k="-80" />
<hkern u1="&#x201c;" u2="&#x153;" k="39" />
<hkern u1="&#x201c;" u2="&#xf8;" k="39" />
<hkern u1="&#x201c;" u2="&#xf6;" k="39" />
<hkern u1="&#x201c;" u2="&#xf5;" k="39" />
<hkern u1="&#x201c;" u2="&#xf4;" k="39" />
<hkern u1="&#x201c;" u2="&#xf3;" k="39" />
<hkern u1="&#x201c;" u2="&#xf2;" k="41" />
<hkern u1="&#x201c;" u2="&#xeb;" k="39" />
<hkern u1="&#x201c;" u2="&#xea;" k="39" />
<hkern u1="&#x201c;" u2="&#xe9;" k="39" />
<hkern u1="&#x201c;" u2="&#xe8;" k="39" />
<hkern u1="&#x201c;" u2="&#xe7;" k="39" />
<hkern u1="&#x201c;" u2="&#xe6;" k="41" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-80" />
<hkern u1="&#x201c;" u2="&#xcf;" k="-41" />
<hkern u1="&#x201c;" u2="&#xce;" k="-41" />
<hkern u1="&#x201c;" u2="&#xcd;" k="-41" />
<hkern u1="&#x201c;" u2="&#xcc;" k="-41" />
<hkern u1="&#x201c;" u2="&#xc6;" k="141" />
<hkern u1="&#x201c;" u2="&#xc5;" k="59" />
<hkern u1="&#x201c;" u2="&#xc4;" k="59" />
<hkern u1="&#x201c;" u2="&#xc3;" k="61" />
<hkern u1="&#x201c;" u2="&#xc2;" k="61" />
<hkern u1="&#x201c;" u2="&#xc1;" k="61" />
<hkern u1="&#x201c;" u2="&#xc0;" k="61" />
<hkern u1="&#x201c;" u2="v" k="-41" />
<hkern u1="&#x201c;" u2="t" k="-41" />
<hkern u1="&#x201c;" u2="q" k="41" />
<hkern u1="&#x201c;" u2="o" k="41" />
<hkern u1="&#x201c;" u2="g" k="20" />
<hkern u1="&#x201c;" u2="e" k="41" />
<hkern u1="&#x201c;" u2="d" k="41" />
<hkern u1="&#x201c;" u2="c" k="41" />
<hkern u1="&#x201c;" u2="Z" k="-41" />
<hkern u1="&#x201c;" u2="Y" k="-82" />
<hkern u1="&#x201c;" u2="X" k="-41" />
<hkern u1="&#x201c;" u2="W" k="-102" />
<hkern u1="&#x201c;" u2="V" k="-82" />
<hkern u1="&#x201c;" u2="T" k="-82" />
<hkern u1="&#x201c;" u2="I" k="-41" />
<hkern u1="&#x201c;" u2="A" k="61" />
</font>
</defs></svg> 