<?php
session_start();
if (!isset($_SESSION['user'])) {
    $_SESSION['logerr'] = "Please login to continue..";
    header("location:index.php");
    exit;
}

include './dbconnect.php';



// Username check endpoint
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['check_username'])) {
    $check_username = trim($_POST['check_username']);
    
    if (empty($check_username)) {
        echo json_encode(['exists' => false]);
        exit;
    }
    
    try {
        $check = $conn->prepare("SELECT COUNT(*) FROM rfc_user_login WHERE user_name = ?");
        $check->execute([$check_username]);
        $count = $check->fetchColumn();
        
        echo json_encode(['exists' => $count > 0]);
        exit;
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error']);
        exit;
    }
}

// echo "<pre>"; 
// print_r($_POST); exit;

// Auto-increment ID for new user
$query = "SELECT MAX(ID) as id FROM rfc_user_login";
$result = $conn->prepare($query);
$result->execute();
$row = $result->fetchObject();
$newid = $row->id + 1;

// Handle form submission
// if ($_SERVER['REQUEST_METHOD'] === 'POST') {
// if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_user'])) {
//     // echo "<pre>"; 
//     // echo "post data:";
//     // print_r($_POST); exit;
//     $user_name = $_POST['user_id'];
//     $user_pass = password_hash($_POST['user_pass'], PASSWORD_DEFAULT);
//     $user_email = $_POST['user_email'];
//     // $user_url = $_POST['user_url'];
//     $display_name = $_POST['display_name'];
//     $user_level = $_POST['user_level'];
//     $pack_authorization = $_POST['pack_authorization'];
//     $is_client_active = $_POST['is_client_active'];
//     // $user_type = $_POST['user_type'];
//     // $user_restaurant_status = $_POST['user_restaurant_status'];
//     $allowed_packages = $_POST['allowed_packages'];
//     $user_client_level = $_POST['user_client_level'];
//     // $activity_status = $_POST['activity_status'];

//     // Check for unique username
//     $check = $conn->prepare("SELECT COUNT(*) FROM rfc_user_login WHERE user_name = ?");
//     $check->execute([$user_name]);

//         if ($check->fetchColumn() > 0) {
//             $error = "Username already exists!";
//         } else {
//             $query = "INSERT INTO rfc_user_login(user_name, user_pass, user_email, user_url, display_name, 
//                     user_level, pack_authorization, is_client_active, user_type, user_restaurant_status, 
//                     allowed_packages, user_client_level, activity_status) 
//                     VALUES('$user_name', '$user_pass', '$user_email', '$user_url', '$display_name', 
//                     '$user_level', '$pack_authorization', '$is_client_active', '$user_type', 
//                     '$user_restaurant_status', '$allowed_packages', '$user_client_level', '$activity_status')";

//                     echo $query; exit;
            
//             $result = $conn->prepare($query);
//             $result->execute();
//             if ($result) {
//                 echo "<script>alert('User added successfully!');</script>";
//                 echo "<script>window.location.href=\"users_list.php\";</script>";
//             }
//         }
//     // if ($check->fetchColumn() > 0) {
//     //     $error = "Username already exists!";
//     // } else {
//     //     $insert = $conn->prepare("INSERT INTO rfc_user_login 
//     //         (user_name, user_pass, user_email, user_url, display_name, user_level, pack_authorization, is_client_active, user_type, user_restaurant_status, allowed_packages, user_client_level, activity_status) 
//     //         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
//     //     $insert->execute([
//     //         $user_name, $user_pass, $user_email, $user_url, $display_name, $user_level, $pack_authorization, $is_client_active, $user_type, $user_restaurant_status, $allowed_packages, $user_client_level, $activity_status
//     //     ]);
//     //     $success = "User added successfully!";
//     // }
// }

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_user'])) {
    // Get form data
    $user_name = $_POST['user_id'];
    $user_pass = password_hash($_POST['user_pass'], PASSWORD_DEFAULT);
    $user_email = $_POST['user_email'] ?? '';
    // $user_url = $_POST['user_url'] ?? '';
    $display_name = $_POST['display_name'];
    $user_level = $_POST['user_level'];
    $pack_authorization = $_POST['pack_authorization'] ?? 0;
    $is_client_active = $_POST['is_client_active'] ?? 1;
    $user_type = $_POST['user_type'] ?? 'U';
    $user_restaurant_status = $_POST['user_restaurant_status'] ?? 0;
    $allowed_packages = $_POST['allowed_packages'] ?? '';
    $user_client_level = $_POST['user_client_level'] ?? 2;
    $activity_status = $_POST['activity_status'] ?? 1;
    $registered_by = $_SESSION['user'] ?? 0;

//    echo $_SESSION['user'];

    // echo $registered_by; exit;

    // Check for unique username
    $check = $conn->prepare("SELECT COUNT(*) FROM rfc_user_login WHERE user_name = ?");
    $check->execute([$user_name]);

    if ($check->fetchColumn() > 0) {
        echo "<script>alert('Username already exists!');</script>";
        echo "<script>window.history.back();</script>";
    } else {
        $query = "INSERT INTO rfc_user_login(
                user_name, 
                user_pass, 
                user_email, 
                display_name, 
                user_level, 
                pack_authorization, 
                is_client_active, 
                user_type, 
                user_restaurant_status, 
                allowed_packages, 
                user_client_level,
                activity_status,
                registered_by
            ) VALUES (
                '$user_name', 
                '$user_pass', 
                '$user_email', 
                '$display_name', 
                '$user_level', 
                '$pack_authorization', 
                '$is_client_active', 
                '$user_type', 
                '$user_restaurant_status', 
                '$allowed_packages', 
                '$user_client_level',
                '$activity_status',
                '$registered_by'
            )";

            // echo "<pre>";
            // echo $query; exit;
        
        $result = $conn->prepare($query);
        $result->execute();
        if ($result) {
            echo "<script>alert('User added successfully!');</script>";
            echo "<script>window.close();</script>";
        } else {
            echo "<script>alert('Error adding user!');</script>";
            echo "<script>window.history.back();</script>";
        }
    }
}
?>